import 'package:flutter/material.dart';
import 'level_system.dart';
import 'level_selection_screen.dart';
import 'level_game_screen.dart';
import 'game_data.dart';

class LevelResultScreen extends StatefulWidget {
  final GameLevel level;
  final int score;
  final int totalQuestions;

  const LevelResultScreen({
    super.key,
    required this.level,
    required this.score,
    required this.totalQuestions,
  });

  @override
  State<LevelResultScreen> createState() => _LevelResultScreenState();
}

class _LevelResultScreenState extends State<LevelResultScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _starsController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _starsAnimation;
  
  int starsEarned = 0;
  bool showRewards = false;

  @override
  void initState() {
    super.initState();
    
    // حساب النجوم
    starsEarned = widget.level.calculateStars(widget.score);
    
    // تهيئة الأنيميشن
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _starsController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: const Interval(0.0, 0.5)),
    );
    
    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    
    _starsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _starsController, curve: Curves.elasticOut),
    );
    
    // بدء الأنيميشن
    _controller.forward();
    
    // تأخير أنيميشن النجوم
    Future.delayed(const Duration(milliseconds: 800), () {
      _starsController.forward();
    });
    
    // إظهار المكافآت بعد النجوم
    Future.delayed(const Duration(milliseconds: 2500), () {
      if (mounted) {
        setState(() {
          showRewards = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _starsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              widget.level.primaryColor,
              widget.level.secondaryColor,
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                      Expanded(
                        child: Text(
                          'نتائج ${widget.level.name}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 48), // لتوسيط العنوان
                    ],
                  ),
                ),

                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Score Card
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(30),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Level Icon
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    color: widget.level.primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    widget.level.icon,
                                    color: Colors.white,
                                    size: 40,
                                  ),
                                ),
                                
                                const SizedBox(height: 20),
                                
                                // Score
                                Text(
                                  '${widget.score}',
                                  style: TextStyle(
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                    color: widget.level.primaryColor,
                                  ),
                                ),
                                
                                Text(
                                  'نقطة',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                
                                const SizedBox(height: 20),
                                
                                // Performance Message
                                Text(
                                  _getPerformanceMessage(),
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 30),

                        // Stars
                        AnimatedBuilder(
                          animation: _starsAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _starsAnimation.value,
                              child: Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Column(
                                  children: [
                                    const Text(
                                      'تقييمك',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 15),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: List.generate(3, (index) {
                                        bool isEarned = index < starsEarned;
                                        return AnimatedContainer(
                                          duration: Duration(milliseconds: 300 + (index * 200)),
                                          margin: const EdgeInsets.symmetric(horizontal: 5),
                                          child: Icon(
                                            isEarned ? Icons.star : Icons.star_border,
                                            color: isEarned ? Colors.amber : Colors.grey[400],
                                            size: 40,
                                          ),
                                        );
                                      }),
                                    ),
                                    const SizedBox(height: 10),
                                    Text(
                                      '$starsEarned من 3 نجوم',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 30),

                        // Statistics
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Column(
                            children: [
                              const Text(
                                'إحصائيات المرحلة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 15),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  _buildStatItem(
                                    icon: Icons.quiz,
                                    label: 'الأسئلة',
                                    value: '${widget.totalQuestions}',
                                    color: Colors.blue,
                                  ),
                                  _buildStatItem(
                                    icon: Icons.timer,
                                    label: 'الوقت المحدد',
                                    value: '${widget.level.timeLimit}ث',
                                    color: Colors.orange,
                                  ),
                                  _buildStatItem(
                                    icon: Icons.trending_up,
                                    label: 'الصعوبة',
                                    value: _getDifficultyText(),
                                    color: _getDifficultyColor(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 30),

                        // Rewards
                        if (showRewards && widget.level.rewards.isNotEmpty)
                          AnimatedOpacity(
                            opacity: showRewards ? 1.0 : 0.0,
                            duration: const Duration(milliseconds: 500),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.amber.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(color: Colors.amber, width: 2),
                              ),
                              child: Column(
                                children: [
                                  const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.card_giftcard, color: Colors.amber, size: 24),
                                      SizedBox(width: 8),
                                      Text(
                                        'المكافآت المحققة',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 15),
                                  ...widget.level.rewards.map((reward) => Padding(
                                    padding: const EdgeInsets.only(bottom: 8),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.check_circle, color: Colors.green, size: 20),
                                        const SizedBox(width: 10),
                                        Expanded(
                                          child: Text(
                                            reward,
                                            style: const TextStyle(
                                              fontSize: 16,
                                              color: Colors.black87,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )).toList(),
                                ],
                              ),
                            ),
                          ),

                        const SizedBox(height: 40),

                        // Action Buttons
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pushAndRemoveUntil(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const LevelSelectionScreen(),
                                    ),
                                    (route) => route.isFirst,
                                  );
                                },
                                icon: const Icon(Icons.list),
                                label: const Text('المراحل'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 15),
                                ),
                              ),
                            ),
                            const SizedBox(width: 15),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  // إعادة لعب المرحلة
                                  Navigator.pop(context);
                                  Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => LevelGameScreen(level: widget.level),
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.refresh),
                                label: const Text('إعادة اللعب'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: widget.level.primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 15),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 5),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _getPerformanceMessage() {
    if (starsEarned == 3) return "أداء مثالي! 🌟";
    if (starsEarned == 2) return "أداء ممتاز! 👏";
    if (starsEarned == 1) return "أداء جيد! 👍";
    return "يمكنك تحسين أدائك! 💪";
  }

  String _getDifficultyText() {
    switch (widget.level.difficulty) {
      case DifficultyLevel.beginner:
        return 'مبتدئ';
      case DifficultyLevel.easy:
        return 'سهل';
      case DifficultyLevel.medium:
        return 'متوسط';
      case DifficultyLevel.hard:
        return 'صعب';
      case DifficultyLevel.expert:
        return 'خبير';
      case DifficultyLevel.genius:
        return 'عبقري';
    }
  }

  Color _getDifficultyColor() {
    switch (widget.level.difficulty) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.easy:
        return Colors.blue;
      case DifficultyLevel.medium:
        return Colors.orange;
      case DifficultyLevel.hard:
        return Colors.red;
      case DifficultyLevel.expert:
        return Colors.purple;
      case DifficultyLevel.genius:
        return Colors.amber;
    }
  }
}
