import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'game_data.dart';

// نظام اللعب المتقدم مع الوقت الحقيقي
class AdvancedGameSystem {
  static const int defaultTimeLimit = 30; // 30 ثانية لكل سؤال
  static const int maxLives = 5; // عدد الأرواح القصوى
  static const int energyRegenTime = 300; // 5 دقائق لتجديد طاقة واحدة
  
  // حالة اللعبة
  static int currentLives = maxLives;
  static int currentEnergy = 100;
  static int totalScore = 0;
  static int streak = 0; // سلسلة الإجابات الصحيحة
  static int bestStreak = 0;
  static DateTime? lastEnergyUpdate;
  static List<String> unlockedAchievements = [];
  
  // تحديث الطاقة بناءً على الوقت
  static void updateEnergy() {
    if (lastEnergyUpdate == null) {
      lastEnergyUpdate = DateTime.now();
      return;
    }
    
    DateTime now = DateTime.now();
    int minutesPassed = now.difference(lastEnergyUpdate!).inMinutes;
    
    if (minutesPassed > 0 && currentEnergy < 100) {
      int energyToAdd = (minutesPassed / 5).floor(); // طاقة واحدة كل 5 دقائق
      currentEnergy = (currentEnergy + energyToAdd).clamp(0, 100);
      lastEnergyUpdate = now;
    }
  }
  
  // استهلاك الطاقة
  static bool consumeEnergy(int amount) {
    updateEnergy();
    if (currentEnergy >= amount) {
      currentEnergy -= amount;
      return true;
    }
    return false;
  }
  
  // فقدان حياة
  static void loseLife() {
    if (currentLives > 0) {
      currentLives--;
      streak = 0; // إعادة تعيين السلسلة
    }
  }
  
  // إضافة نقاط مع مضاعف السلسلة
  static int addScore(int basePoints, bool isCorrect) {
    if (isCorrect) {
      streak++;
      if (streak > bestStreak) {
        bestStreak = streak;
      }
      
      // مضاعف السلسلة
      double multiplier = 1.0 + (streak * 0.1); // 10% إضافية لكل إجابة صحيحة متتالية
      int finalPoints = (basePoints * multiplier).round();
      totalScore += finalPoints;
      
      // فحص الإنجازات
      checkAchievements();
      
      return finalPoints;
    } else {
      streak = 0;
      loseLife();
      return 0;
    }
  }
  
  // فحص الإنجازات
  static void checkAchievements() {
    List<Map<String, dynamic>> achievements = [
      {'id': 'first_correct', 'name': 'البداية', 'description': 'أجب على أول سؤال بشكل صحيح', 'condition': totalScore > 0},
      {'id': 'streak_5', 'name': 'متتالي', 'description': 'أجب على 5 أسئلة متتالية بشكل صحيح', 'condition': streak >= 5},
      {'id': 'streak_10', 'name': 'نار!', 'description': 'أجب على 10 أسئلة متتالية بشكل صحيح', 'condition': streak >= 10},
      {'id': 'score_100', 'name': 'مئوي', 'description': 'احصل على 100 نقطة', 'condition': totalScore >= 100},
      {'id': 'score_500', 'name': 'خمسمئة', 'description': 'احصل على 500 نقطة', 'condition': totalScore >= 500},
      {'id': 'score_1000', 'name': 'ألفي', 'description': 'احصل على 1000 نقطة', 'condition': totalScore >= 1000},
    ];
    
    for (var achievement in achievements) {
      if (achievement['condition'] && !unlockedAchievements.contains(achievement['id'])) {
        unlockedAchievements.add(achievement['id']);
        // يمكن إضافة إشعار هنا
      }
    }
  }
  
  // إعادة تعيين اللعبة
  static void resetGame() {
    currentLives = maxLives;
    currentEnergy = 100;
    totalScore = 0;
    streak = 0;
    lastEnergyUpdate = DateTime.now();
  }
  
  // حفظ التقدم (يمكن تطويره لاحقاً مع قاعدة البيانات)
  static Map<String, dynamic> saveProgress() {
    return {
      'lives': currentLives,
      'energy': currentEnergy,
      'score': totalScore,
      'streak': streak,
      'bestStreak': bestStreak,
      'achievements': unlockedAchievements,
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }
  
  // تحميل التقدم
  static void loadProgress(Map<String, dynamic> data) {
    currentLives = data['lives'] ?? maxLives;
    currentEnergy = data['energy'] ?? 100;
    totalScore = data['score'] ?? 0;
    streak = data['streak'] ?? 0;
    bestStreak = data['bestStreak'] ?? 0;
    unlockedAchievements = List<String>.from(data['achievements'] ?? []);
    if (data['lastUpdate'] != null) {
      lastEnergyUpdate = DateTime.parse(data['lastUpdate']);
    }
  }
}

// مؤقت اللعبة
class GameTimer {
  Timer? _timer;
  int _timeLeft = 0;
  final VoidCallback onTimeUp;
  final Function(int) onTick;
  
  GameTimer({required this.onTimeUp, required this.onTick});
  
  void start(int seconds) {
    stop();
    _timeLeft = seconds;
    onTick(_timeLeft);
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _timeLeft--;
      onTick(_timeLeft);
      
      if (_timeLeft <= 0) {
        stop();
        onTimeUp();
      }
    });
  }
  
  void stop() {
    _timer?.cancel();
    _timer = null;
  }
  
  void pause() {
    _timer?.cancel();
  }
  
  void resume() {
    if (_timeLeft > 0) {
      start(_timeLeft);
    }
  }
  
  int get timeLeft => _timeLeft;
  bool get isRunning => _timer?.isActive ?? false;
  
  void dispose() {
    stop();
  }
}

// ألغاز تفاعلية متقدمة
class AdvancedPuzzleTypes {
  static final Random _random = Random();
  
  // لغز الرسم
  static Question createDrawingPuzzle(int id) {
    List<String> shapes = ['دائرة', 'مربع', 'مثلث', 'نجمة', 'قلب'];
    String shape = shapes[_random.nextInt(shapes.length)];
    
    return Question(
      id: id,
      questionText: "ارسم $shape",
      questionType: QuestionType.draw,
      difficulty: DifficultyLevel.medium,
      category: QuestionCategory.art,
      correctAnswer: shape,
      hint: "استخدم إصبعك لرسم الشكل المطلوب",
      explanation: "كان عليك رسم $shape",
      points: 20,
      tags: ['رسم', 'أشكال'],
      extraData: {
        'targetShape': shape,
        'timeLimit': 45,
      },
    );
  }
  
  // لغز التدوير
  static Question createRotationPuzzle(int id) {
    int targetAngle = [90, 180, 270][_random.nextInt(3)];
    
    return Question(
      id: id,
      questionText: "أدر الشكل $targetAngle درجة",
      questionType: QuestionType.rotate,
      difficulty: DifficultyLevel.medium,
      category: QuestionCategory.general,
      correctAnswer: targetAngle.toString(),
      hint: "استخدم إصبعك لتدوير الشكل",
      explanation: "كان عليك تدوير الشكل $targetAngle درجة",
      points: 15,
      tags: ['تدوير', 'حركة'],
      extraData: {
        'targetAngle': targetAngle,
        'timeLimit': 30,
      },
    );
  }
  
  // لغز الهز
  static Question createShakePuzzle(int id) {
    List<String> items = ['شجرة التفاح', 'علبة الدواء', 'زجاجة الماء', 'كيس الحلوى'];
    String item = items[_random.nextInt(items.length)];
    
    return Question(
      id: id,
      questionText: "هز الجهاز لتحريك $item!",
      questionType: QuestionType.shake,
      difficulty: DifficultyLevel.easy,
      category: QuestionCategory.general,
      correctAnswer: "shake",
      hint: "هز جهازك بلطف",
      explanation: "كان عليك هز الجهاز لتحريك $item",
      points: 10,
      tags: ['هز', 'حركة'],
      extraData: {
        'item': item,
        'timeLimit': 20,
        'shakeThreshold': 2.0,
      },
    );
  }
  
  // لغز الصوت
  static Question createSoundPuzzle(int id) {
    Map<String, String> sounds = {
      'قط': 'مواء',
      'كلب': 'نباح',
      'بقرة': 'خوار',
      'ديك': 'صياح',
      'أسد': 'زئير',
    };
    
    String animal = sounds.keys.elementAt(_random.nextInt(sounds.length));
    String sound = sounds[animal]!;
    
    return Question(
      id: id,
      questionText: "اضغط على الزر لسماع الصوت، ثم اختر الحيوان الصحيح",
      questionType: QuestionType.sound,
      difficulty: DifficultyLevel.easy,
      category: QuestionCategory.animals,
      correctAnswer: animal,
      options: sounds.keys.toList()..shuffle(),
      hint: "استمع بعناية للصوت",
      explanation: "الصوت كان $sound، وهو صوت $animal",
      points: 15,
      tags: ['صوت', 'حيوانات'],
      extraData: {
        'soundFile': '${animal}_sound.mp3',
        'timeLimit': 25,
      },
    );
  }
  
  // لغز التكبير/التصغير
  static Question createZoomPuzzle(int id) {
    List<String> objects = ['خريطة', 'صورة', 'نص', 'رسم'];
    String object = objects[_random.nextInt(objects.length)];
    bool zoomIn = _random.nextBool();
    
    return Question(
      id: id,
      questionText: "${zoomIn ? 'كبر' : 'صغر'} $object للحصول على التفاصيل",
      questionType: QuestionType.zoom,
      difficulty: DifficultyLevel.easy,
      category: QuestionCategory.general,
      correctAnswer: zoomIn ? 'zoom_in' : 'zoom_out',
      hint: "استخدم إيماءات التكبير والتصغير",
      explanation: "كان عليك ${zoomIn ? 'تكبير' : 'تصغير'} $object",
      points: 10,
      tags: ['تكبير', 'إيماءات'],
      extraData: {
        'object': object,
        'zoomIn': zoomIn,
        'timeLimit': 20,
      },
    );
  }
}

// مدير التأثيرات البصرية
class VisualEffectsManager {
  static void showCorrectAnswer(BuildContext context) {
    // تأثير الإجابة الصحيحة
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CorrectAnswerDialog(),
    );
  }
  
  static void showWrongAnswer(BuildContext context) {
    // تأثير الإجابة الخاطئة
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const WrongAnswerDialog(),
    );
  }
  
  static void showTimeUpDialog(BuildContext context) {
    // تأثير انتهاء الوقت
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const TimeUpDialog(),
    );
  }
}

// حوار الإجابة الصحيحة
class CorrectAnswerDialog extends StatefulWidget {
  const CorrectAnswerDialog({super.key});

  @override
  State<CorrectAnswerDialog> createState() => _CorrectAnswerDialogState();
}

class _CorrectAnswerDialogState extends State<CorrectAnswerDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _controller.forward();
    
    // إغلاق تلقائي بعد ثانيتين
    Timer(const Duration(seconds: 2), () {
      if (mounted) Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 60,
                  ),
                  SizedBox(height: 10),
                  Text(
                    '✅ صحيح!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// حوار الإجابة الخاطئة
class WrongAnswerDialog extends StatefulWidget {
  const WrongAnswerDialog({super.key});

  @override
  State<WrongAnswerDialog> createState() => _WrongAnswerDialogState();
}

class _WrongAnswerDialogState extends State<WrongAnswerDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(begin: -10.0, end: 10.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticIn),
    );
    _controller.repeat(reverse: true);
    
    // إغلاق تلقائي بعد ثانيتين
    Timer(const Duration(seconds: 2), () {
      if (mounted) Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _shakeAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(_shakeAnimation.value, 0),
            child: Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 60,
                  ),
                  SizedBox(height: 10),
                  Text(
                    '❌ خطأ!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// حوار انتهاء الوقت
class TimeUpDialog extends StatelessWidget {
  const TimeUpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.timer_off,
              color: Colors.white,
              size: 60,
            ),
            SizedBox(height: 10),
            Text(
              '⏰ انتهى الوقت!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
