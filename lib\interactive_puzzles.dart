import 'package:flutter/material.dart';
import 'dart:math';
import 'game_data.dart';
import 'sound_system.dart';

// نظام الألغاز التفاعلية المتقدمة
class InteractivePuzzleWidget extends StatefulWidget {
  final Question question;
  final Function(String) onAnswer;

  const InteractivePuzzleWidget({
    super.key,
    required this.question,
    required this.onAnswer,
  });

  @override
  State<InteractivePuzzleWidget> createState() => _InteractivePuzzleWidgetState();
}

class _InteractivePuzzleWidgetState extends State<InteractivePuzzleWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  
  List<Offset> drawnPoints = [];
  bool isDrawing = false;
  double currentRotation = 0;
  double currentScale = 1.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * pi).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.question.questionType) {
      case QuestionType.draw:
        return _buildDrawingPuzzle();
      case QuestionType.rotate:
        return _buildRotationPuzzle();
      case QuestionType.zoom:
        return _buildZoomPuzzle();
      case QuestionType.drag:
        return _buildDragPuzzle();
      case QuestionType.sequence:
        return _buildSequencePuzzle();
      case QuestionType.memory:
        return _buildMemoryPuzzle();
      default:
        return _buildDefaultPuzzle();
    }
  }

  // لغز الرسم
  Widget _buildDrawingPuzzle() {
    return Column(
      children: [
        Text(
          'ارسم الشكل المطلوب:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        Container(
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey, width: 2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                isDrawing = true;
                drawnPoints.clear();
                drawnPoints.add(details.localPosition);
              });
              HapticSystem.lightImpact();
            },
            onPanUpdate: (details) {
              if (isDrawing) {
                setState(() {
                  drawnPoints.add(details.localPosition);
                });
              }
            },
            onPanEnd: (details) {
              setState(() {
                isDrawing = false;
              });
              _analyzeDrawing();
            },
            child: CustomPaint(
              painter: DrawingPainter(drawnPoints),
              size: const Size(300, 300),
            ),
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: () {
                setState(() {
                  drawnPoints.clear();
                });
                SoundSystem.playClickSound();
              },
              child: const Text('مسح'),
            ),
            ElevatedButton(
              onPressed: () {
                _analyzeDrawing();
              },
              child: const Text('تحقق'),
            ),
          ],
        ),
      ],
    );
  }

  // لغز التدوير
  Widget _buildRotationPuzzle() {
    return Column(
      children: [
        Text(
          'أدر الشكل للزاوية المطلوبة:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        GestureDetector(
          onPanUpdate: (details) {
            setState(() {
              currentRotation += details.delta.dx * 0.01;
            });
            HapticSystem.lightImpact();
          },
          child: AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: currentRotation,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Center(
                    child: Icon(Icons.arrow_upward, size: 50, color: Colors.white),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 20),
        Text(
          'الزاوية الحالية: ${(currentRotation * 180 / pi).round()}°',
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            _checkRotation();
          },
          child: const Text('تحقق من الزاوية'),
        ),
      ],
    );
  }

  // لغز التكبير/التصغير
  Widget _buildZoomPuzzle() {
    return Column(
      children: [
        Text(
          'كبر أو صغر الشكل للحجم المطلوب:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        GestureDetector(
          onScaleUpdate: (details) {
            setState(() {
              currentScale = details.scale.clamp(0.5, 2.0);
            });
            HapticSystem.lightImpact();
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: currentScale,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: Icon(Icons.zoom_in, size: 40, color: Colors.white),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 20),
        Text(
          'الحجم الحالي: ${(currentScale * 100).round()}%',
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            _checkScale();
          },
          child: const Text('تحقق من الحجم'),
        ),
      ],
    );
  }

  // لغز السحب والإفلات المتقدم
  Widget _buildDragPuzzle() {
    return Column(
      children: [
        Text(
          'رتب العناصر في الترتيب الصحيح:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        _buildDragDropArea(),
      ],
    );
  }

  // لغز التسلسل التفاعلي
  Widget _buildSequencePuzzle() {
    return Column(
      children: [
        Text(
          'اتبع التسلسل واضغط على الأزرار بالترتيب الصحيح:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        _buildSequenceButtons(),
      ],
    );
  }

  // لغز الذاكرة التفاعلي
  Widget _buildMemoryPuzzle() {
    return Column(
      children: [
        Text(
          'تذكر الألوان واضغط عليها بنفس الترتيب:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        _buildMemoryGrid(),
      ],
    );
  }

  // لغز افتراضي
  Widget _buildDefaultPuzzle() {
    return Column(
      children: [
        Text(
          widget.question.questionText,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        if (widget.question.options != null)
          ...widget.question.options!.map((option) => Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 10),
            child: ElevatedButton(
              onPressed: () {
                SoundSystem.playClickSound();
                HapticSystem.selectionClick();
                widget.onAnswer(option);
              },
              child: Text(option),
            ),
          )).toList(),
      ],
    );
  }

  // منطقة السحب والإفلات
  Widget _buildDragDropArea() {
    List<String> items = ['1', '2', '3', '4'];
    List<String> targets = ['A', 'B', 'C', 'D'];
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Column(
          children: items.map((item) => Draggable<String>(
            data: item,
            feedback: Container(
              width: 50,
              height: 50,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: Center(child: Text(item, style: const TextStyle(color: Colors.white))),
            ),
            child: Container(
              width: 50,
              height: 50,
              margin: const EdgeInsets.all(5),
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: Center(child: Text(item, style: const TextStyle(color: Colors.white))),
            ),
          )).toList(),
        ),
        Column(
          children: targets.map((target) => DragTarget<String>(
            onAcceptWithDetails: (details) {
              SoundSystem.playCorrectSound();
              HapticSystem.mediumImpact();
              // فحص الترتيب الصحيح
              _checkDragOrder(details.data, target);
            },
            builder: (context, candidateData, rejectedData) {
              return Container(
                width: 60,
                height: 60,
                margin: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: candidateData.isNotEmpty ? Colors.green.withValues(alpha: 0.3) : Colors.grey[300],
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(child: Text(target)),
              );
            },
          )).toList(),
        ),
      ],
    );
  }

  // أزرار التسلسل
  Widget _buildSequenceButtons() {
    List<Color> colors = [Colors.red, Colors.blue, Colors.green, Colors.yellow];
    
    return GridView.builder(
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            SoundSystem.playClickSound();
            HapticSystem.lightImpact();
            _animationController.forward().then((_) {
              _animationController.reverse();
            });
            // فحص التسلسل
            _checkSequence(index);
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: colors[index],
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: colors[index].withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // شبكة الذاكرة
  Widget _buildMemoryGrid() {
    List<Color> colors = [Colors.red, Colors.blue, Colors.green, Colors.yellow, Colors.purple, Colors.orange];
    
    return GridView.builder(
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            SoundSystem.playClickSound();
            HapticSystem.lightImpact();
            _checkMemorySequence(index);
          },
          child: Container(
            decoration: BoxDecoration(
              color: colors[index],
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      },
    );
  }

  // تحليل الرسم
  void _analyzeDrawing() {
    if (drawnPoints.length < 10) {
      widget.onAnswer('incomplete');
      return;
    }
    
    // تحليل بسيط للشكل المرسوم
    String targetShape = widget.question.extraData?['targetShape'] ?? 'circle';
    
    if (targetShape == 'circle') {
      // فحص إذا كان الرسم يشبه الدائرة
      if (_isCircleShape()) {
        widget.onAnswer('circle');
      } else {
        widget.onAnswer('not_circle');
      }
    }
  }

  // فحص إذا كان الشكل دائرة
  bool _isCircleShape() {
    if (drawnPoints.length < 20) return false;
    
    // حساب المركز
    double centerX = drawnPoints.map((p) => p.dx).reduce((a, b) => a + b) / drawnPoints.length;
    double centerY = drawnPoints.map((p) => p.dy).reduce((a, b) => a + b) / drawnPoints.length;
    
    // حساب المسافات من المركز
    List<double> distances = drawnPoints.map((p) => 
      sqrt(pow(p.dx - centerX, 2) + pow(p.dy - centerY, 2))
    ).toList();
    
    // فحص إذا كانت المسافات متقاربة (دائرة)
    double avgDistance = distances.reduce((a, b) => a + b) / distances.length;
    double variance = distances.map((d) => pow(d - avgDistance, 2)).reduce((a, b) => a + b) / distances.length;
    
    return variance < 500; // عتبة التباين للدائرة
  }

  // فحص التدوير
  void _checkRotation() {
    double targetAngle = widget.question.extraData?['targetAngle']?.toDouble() ?? 90.0;
    double currentDegrees = (currentRotation * 180 / pi) % 360;
    
    if ((currentDegrees - targetAngle).abs() < 15) { // هامش خطأ 15 درجة
      widget.onAnswer(targetAngle.toString());
    } else {
      widget.onAnswer('wrong_angle');
    }
  }

  // فحص التكبير
  void _checkScale() {
    bool zoomIn = widget.question.extraData?['zoomIn'] ?? true;
    
    if (zoomIn && currentScale > 1.2) {
      widget.onAnswer('zoom_in');
    } else if (!zoomIn && currentScale < 0.8) {
      widget.onAnswer('zoom_out');
    } else {
      widget.onAnswer('wrong_scale');
    }
  }

  // فحص ترتيب السحب
  void _checkDragOrder(String item, String target) {
    // منطق فحص الترتيب الصحيح
    Map<String, String> correctOrder = {'1': 'A', '2': 'B', '3': 'C', '4': 'D'};
    
    if (correctOrder[item] == target) {
      widget.onAnswer('correct_order');
    } else {
      widget.onAnswer('wrong_order');
    }
  }

  // فحص التسلسل
  void _checkSequence(int buttonIndex) {
    // منطق فحص التسلسل الصحيح
    List<int> correctSequence = [0, 1, 2, 3]; // التسلسل المطلوب
    
    // هنا يمكن إضافة منطق أكثر تعقيداً لتتبع التسلسل
    widget.onAnswer('sequence_$buttonIndex');
  }

  // فحص تسلسل الذاكرة
  void _checkMemorySequence(int colorIndex) {
    // منطق فحص تسلسل الذاكرة
    widget.onAnswer('memory_$colorIndex');
  }
}

// رسام الخطوط المرسومة
class DrawingPainter extends CustomPainter {
  final List<Offset> points;

  DrawingPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    if (points.isEmpty) return;

    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 3.0
      ..strokeCap = StrokeCap.round;

    for (int i = 0; i < points.length - 1; i++) {
      canvas.drawLine(points[i], points[i + 1], paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
