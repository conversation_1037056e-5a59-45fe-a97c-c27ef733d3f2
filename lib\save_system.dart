import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'advanced_game_system.dart';
import 'level_system.dart';

class SaveSystem {
  static const String _keyTotalScore = 'total_score';
  static const String _keyBestStreak = 'best_streak';
  static const String _keyCurrentLives = 'current_lives';
  static const String _keyCurrentEnergy = 'current_energy';
  static const String _keyLastEnergyUpdate = 'last_energy_update';
  static const String _keyAchievements = 'achievements';
  static const String _keyLevelsData = 'levels_data';
  static const String _keyGameSettings = 'game_settings';
  static const String _keyFirstTime = 'first_time';

  // حفظ تقدم اللعبة
  static Future<void> saveGameProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ بيانات النظام المتقدم
      await prefs.setInt(_keyTotalScore, AdvancedGameSystem.totalScore);
      await prefs.setInt(_keyBestStreak, AdvancedGameSystem.bestStreak);
      await prefs.setInt(_keyCurrentLives, AdvancedGameSystem.currentLives);
      await prefs.setInt(_keyCurrentEnergy, AdvancedGameSystem.currentEnergy);
      
      if (AdvancedGameSystem.lastEnergyUpdate != null) {
        await prefs.setString(_keyLastEnergyUpdate, 
            AdvancedGameSystem.lastEnergyUpdate!.toIso8601String());
      }
      
      // حفظ الإنجازات
      await prefs.setStringList(_keyAchievements, AdvancedGameSystem.unlockedAchievements);
      
      // حفظ بيانات المراحل
      await _saveLevelsData(prefs);
      
    } catch (e) {
      debugPrint('خطأ في حفظ التقدم: $e');
    }
  }

  // تحميل تقدم اللعبة
  static Future<void> loadGameProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل بيانات النظام المتقدم
      AdvancedGameSystem.totalScore = prefs.getInt(_keyTotalScore) ?? 0;
      AdvancedGameSystem.bestStreak = prefs.getInt(_keyBestStreak) ?? 0;
      AdvancedGameSystem.currentLives = prefs.getInt(_keyCurrentLives) ?? 5;
      AdvancedGameSystem.currentEnergy = prefs.getInt(_keyCurrentEnergy) ?? 100;
      
      String? lastUpdateStr = prefs.getString(_keyLastEnergyUpdate);
      if (lastUpdateStr != null) {
        AdvancedGameSystem.lastEnergyUpdate = DateTime.parse(lastUpdateStr);
      }
      
      // تحميل الإنجازات
      AdvancedGameSystem.unlockedAchievements = 
          prefs.getStringList(_keyAchievements) ?? [];
      
      // تحميل بيانات المراحل
      await _loadLevelsData(prefs);
      
      // تحديث الطاقة بناءً على الوقت المنقضي
      AdvancedGameSystem.updateEnergy();
      
    } catch (e) {
      debugPrint('خطأ في تحميل التقدم: $e');
    }
  }

  // حفظ بيانات المراحل
  static Future<void> _saveLevelsData(SharedPreferences prefs) async {
    try {
      List<GameLevel> levels = LevelManager.getAllLevels();
      List<Map<String, dynamic>> levelsJson = levels.map((level) => {
        'id': level.id,
        'isUnlocked': level.isUnlocked,
        'starsEarned': level.starsEarned,
        'bestScore': level.bestScore,
      }).toList();
      
      String jsonString = jsonEncode(levelsJson);
      await prefs.setString(_keyLevelsData, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات المراحل: $e');
    }
  }

  // تحميل بيانات المراحل
  static Future<void> _loadLevelsData(SharedPreferences prefs) async {
    try {
      String? jsonString = prefs.getString(_keyLevelsData);
      if (jsonString != null) {
        List<dynamic> levelsJson = jsonDecode(jsonString);
        
        // تحديث بيانات المراحل
        for (var levelData in levelsJson) {
          int levelId = levelData['id'];
          bool isUnlocked = levelData['isUnlocked'] ?? false;
          int starsEarned = levelData['starsEarned'] ?? 0;
          int bestScore = levelData['bestScore'] ?? 0;
          
          // تحديث المرحلة في النظام
          _updateLevelData(levelId, isUnlocked, starsEarned, bestScore);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المراحل: $e');
    }
  }

  // تحديث بيانات مرحلة محددة
  static void _updateLevelData(int levelId, bool isUnlocked, int starsEarned, int bestScore) {
    // هذه الدالة ستحتاج تحديث في LevelManager
    // سنضيفها لاحقاً
  }

  // حفظ إعدادات اللعبة
  static Future<void> saveGameSettings({
    bool? soundEnabled,
    bool? musicEnabled,
    double? soundVolume,
    double? musicVolume,
    String? language,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      Map<String, dynamic> settings = {
        'soundEnabled': soundEnabled ?? true,
        'musicEnabled': musicEnabled ?? true,
        'soundVolume': soundVolume ?? 1.0,
        'musicVolume': musicVolume ?? 0.5,
        'language': language ?? 'ar',
      };
      
      String jsonString = jsonEncode(settings);
      await prefs.setString(_keyGameSettings, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ الإعدادات: $e');
    }
  }

  // تحميل إعدادات اللعبة
  static Future<Map<String, dynamic>> loadGameSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? jsonString = prefs.getString(_keyGameSettings);
      
      if (jsonString != null) {
        return jsonDecode(jsonString);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
    
    // الإعدادات الافتراضية
    return {
      'soundEnabled': true,
      'musicEnabled': true,
      'soundVolume': 1.0,
      'musicVolume': 0.5,
      'language': 'ar',
    };
  }

  // فحص إذا كانت هذه أول مرة للعب
  static Future<bool> isFirstTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyFirstTime) ?? true;
    } catch (e) {
      return true;
    }
  }

  // تعيين أن اللاعب لعب من قبل
  static Future<void> setNotFirstTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyFirstTime, false);
    } catch (e) {
      debugPrint('خطأ في تعيين حالة اللعب: $e');
    }
  }

  // مسح جميع البيانات المحفوظة
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      // إعادة تعيين البيانات للقيم الافتراضية
      AdvancedGameSystem.resetGame();
      LevelManager.initializeLevels();
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
    }
  }

  // حفظ إحصائيات اللعبة
  static Future<void> saveGameStats({
    int? totalGamesPlayed,
    int? totalQuestionsAnswered,
    int? totalCorrectAnswers,
    int? totalTimeSpent,
    DateTime? lastPlayDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (totalGamesPlayed != null) {
        await prefs.setInt('total_games_played', totalGamesPlayed);
      }
      if (totalQuestionsAnswered != null) {
        await prefs.setInt('total_questions_answered', totalQuestionsAnswered);
      }
      if (totalCorrectAnswers != null) {
        await prefs.setInt('total_correct_answers', totalCorrectAnswers);
      }
      if (totalTimeSpent != null) {
        await prefs.setInt('total_time_spent', totalTimeSpent);
      }
      if (lastPlayDate != null) {
        await prefs.setString('last_play_date', lastPlayDate.toIso8601String());
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الإحصائيات: $e');
    }
  }

  // تحميل إحصائيات اللعبة
  static Future<Map<String, dynamic>> loadGameStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return {
        'totalGamesPlayed': prefs.getInt('total_games_played') ?? 0,
        'totalQuestionsAnswered': prefs.getInt('total_questions_answered') ?? 0,
        'totalCorrectAnswers': prefs.getInt('total_correct_answers') ?? 0,
        'totalTimeSpent': prefs.getInt('total_time_spent') ?? 0,
        'lastPlayDate': prefs.getString('last_play_date'),
      };
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
      return {
        'totalGamesPlayed': 0,
        'totalQuestionsAnswered': 0,
        'totalCorrectAnswers': 0,
        'totalTimeSpent': 0,
        'lastPlayDate': null,
      };
    }
  }

  // حفظ تلقائي كل فترة
  static void startAutoSave() {
    Timer.periodic(const Duration(minutes: 1), (timer) {
      saveGameProgress();
    });
  }
}


