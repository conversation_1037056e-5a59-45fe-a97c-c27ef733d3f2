import 'package:flutter/material.dart';
import 'dart:async';
import 'level_system.dart';
import 'game_data.dart';
import 'advanced_game_system.dart';
import 'level_result_screen.dart';

class LevelGameScreen extends StatefulWidget {
  final GameLevel level;

  const LevelGameScreen({super.key, required this.level});

  @override
  State<LevelGameScreen> createState() => _LevelGameScreenState();
}

class _LevelGameScreenState extends State<LevelGameScreen>
    with TickerProviderStateMixin {
  int currentQuestionIndex = 0;
  int levelScore = 0;
  bool showHint = false;
  bool questionAnswered = false;
  String userAnswer = '';
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  TextEditingController textController = TextEditingController();
  List<Question> levelQuestions = [];
  
  // نظام المؤقت
  late GameTimer gameTimer;
  int timeLeft = 0;
  bool isPaused = false;

  @override
  void initState() {
    super.initState();
    
    // تهيئة الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
    
    // تحميل أسئلة المرحلة
    levelQuestions = LevelManager.getLevelQuestions(widget.level.id);
    
    // تهيئة المؤقت
    gameTimer = GameTimer(
      onTimeUp: _onTimeUp,
      onTick: _onTimerTick,
    );
    
    // بدء المؤقت للسؤال الأول
    _startQuestionTimer();
    _progressController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _progressController.dispose();
    textController.dispose();
    gameTimer.dispose();
    super.dispose();
  }

  Question get currentQuestion => levelQuestions[currentQuestionIndex];
  
  void _startQuestionTimer() {
    gameTimer.start(widget.level.timeLimit);
  }
  
  void _onTimeUp() {
    if (!questionAnswered) {
      setState(() {
        questionAnswered = true;
        userAnswer = 'timeout';
      });
      
      _showTimeUpDialog();
      
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.of(context).pop();
          _nextQuestion();
        }
      });
    }
  }
  
  void _onTimerTick(int time) {
    setState(() {
      timeLeft = time;
    });
  }
  
  void _nextQuestion() {
    if (currentQuestionIndex < levelQuestions.length - 1) {
      setState(() {
        currentQuestionIndex++;
        questionAnswered = false;
        showHint = false;
        userAnswer = '';
        textController.clear();
      });
      _startQuestionTimer();
      _updateProgress();
    } else {
      _endLevel();
    }
  }
  
  void _updateProgress() {
    double progress = (currentQuestionIndex + 1) / levelQuestions.length;
    _progressController.animateTo(progress);
  }
  
  void _endLevel() {
    gameTimer.stop();
    
    // حفظ نتيجة المرحلة
    LevelManager.completeLevel(widget.level.id, levelScore);
    
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LevelResultScreen(
            level: widget.level,
            score: levelScore,
            totalQuestions: levelQuestions.length,
          ),
        ),
      );
    }
  }

  void checkAnswer(String answer) {
    if (questionAnswered) return;
    
    gameTimer.stop();
    
    setState(() {
      questionAnswered = true;
      userAnswer = answer;
    });

    bool isCorrect = answer.toLowerCase() == currentQuestion.correctAnswer.toLowerCase();
    
    if (isCorrect) {
      levelScore += currentQuestion.points;
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
      _showCorrectDialog();
    } else {
      _showWrongDialog();
    }

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop();
        _nextQuestion();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              widget.level.primaryColor.withValues(alpha: 0.8),
              widget.level.secondaryColor.withValues(alpha: 0.6),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Top Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () => _showExitDialog(),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                        Text(
                          widget.level.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: timeLeft <= 10 ? Colors.red : Colors.orange,
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.timer, color: Colors.white, size: 16),
                              const SizedBox(width: 4),
                              Text(
                                '$timeLeft',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 15),
                    
                    // Progress Bar
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'السؤال ${currentQuestionIndex + 1} من ${levelQuestions.length}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              'النقاط: $levelScore',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return LinearProgressIndicator(
                              value: _progressAnimation.value,
                              backgroundColor: Colors.white.withValues(alpha: 0.3),
                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                              minHeight: 6,
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Question Area
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Question
                      AnimatedBuilder(
                        animation: _scaleAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _scaleAnimation.value,
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: widget.level.primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(
                                  color: widget.level.primaryColor,
                                  width: 2,
                                ),
                              ),
                              child: Text(
                                currentQuestion.questionText,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          );
                        },
                      ),
                      
                      const SizedBox(height: 30),
                      
                      // Answer Area
                      Expanded(
                        child: _buildQuestionWidget(),
                      ),
                      
                      // Hint Button
                      if (!questionAnswered)
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              showHint = !showHint;
                            });
                          },
                          icon: const Icon(Icons.lightbulb),
                          label: Text(showHint ? 'إخفاء التلميح' : 'تلميح'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.amber,
                            foregroundColor: Colors.black,
                          ),
                        ),
                      
                      // Hint
                      if (showHint)
                        Container(
                          margin: const EdgeInsets.only(top: 10),
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Colors.amber.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.amber),
                          ),
                          child: Text(
                            '💡 ${currentQuestion.hint}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionWidget() {
    switch (currentQuestion.questionType) {
      case QuestionType.multipleChoice:
        return _buildMultipleChoiceQuestion();
      case QuestionType.input:
      case QuestionType.math:
      case QuestionType.sequence:
        return _buildInputQuestion();
      default:
        return _buildMultipleChoiceQuestion();
    }
  }

  Widget _buildMultipleChoiceQuestion() {
    List<String> options = currentQuestion.options ?? ['نعم', 'لا'];
    
    return Column(
      children: options.map((option) {
        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 10),
          child: ElevatedButton(
            onPressed: questionAnswered ? null : () => checkAnswer(option),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.level.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(15),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: Text(
              option,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInputQuestion() {
    return Column(
      children: [
        TextField(
          controller: textController,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          decoration: InputDecoration(
            hintText: 'أدخل الإجابة',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(color: widget.level.primaryColor, width: 2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(color: widget.level.primaryColor, width: 3),
            ),
          ),
          onSubmitted: (value) => checkAnswer(value),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: questionAnswered ? null : () => checkAnswer(textController.text),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.level.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
          ),
          child: const Text('تأكيد', style: TextStyle(fontSize: 18)),
        ),
      ],
    );
  }

  void _showCorrectDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CorrectAnswerDialog(),
    );
  }

  void _showWrongDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const WrongAnswerDialog(),
    );
  }

  void _showTimeUpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const TimeUpDialog(),
    );
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنهاء المرحلة؟'),
        content: const Text('هل أنت متأكد من أنك تريد إنهاء المرحلة؟ ستفقد التقدم الحالي.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('إنهاء'),
          ),
        ],
      ),
    );
  }
}

// استيراد الحوارات من النظام المتقدم
class CorrectAnswerDialog extends StatelessWidget {
  const CorrectAnswerDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 60),
            SizedBox(height: 10),
            Text(
              '✅ صحيح!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WrongAnswerDialog extends StatelessWidget {
  const WrongAnswerDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.close, color: Colors.white, size: 60),
            SizedBox(height: 10),
            Text(
              '❌ خطأ!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TimeUpDialog extends StatelessWidget {
  const TimeUpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.timer_off, color: Colors.white, size: 60),
            SizedBox(height: 10),
            Text(
              '⏰ انتهى الوقت!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
