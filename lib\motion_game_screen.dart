import 'package:flutter/material.dart';
import 'dart:async';
import 'motion_puzzle_system.dart';
import 'motion_puzzle_widgets.dart';
import 'sound_system.dart';
import 'advanced_game_system.dart';

class MotionGameScreen extends StatefulWidget {
  const MotionGameScreen({super.key});

  @override
  State<MotionGameScreen> createState() => _MotionGameScreenState();
}

class _MotionGameScreenState extends State<MotionGameScreen>
    with TickerProviderStateMixin {
  late AnimationController _transitionController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  MotionPuzzle? currentPuzzle;
  int totalScore = 0;
  int puzzlesSolved = 0;
  bool isLoading = true;
  bool showResults = false;

  @override
  void initState() {
    super.initState();
    
    _transitionController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeIn,
    ));
    
    _initializeGame();
  }

  void _initializeGame() async {
    // Initialize motion puzzle system
    MotionPuzzleManager.initialize();
    
    await Future.delayed(const Duration(milliseconds: 1500));
    
    setState(() {
      currentPuzzle = MotionPuzzleManager.getCurrentPuzzle();
      isLoading = false;
    });
    
    _transitionController.forward();
  }

  @override
  void dispose() {
    _transitionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: SafeArea(
          child: isLoading ? _buildLoadingScreen() : _buildGameScreen(),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Loading animation
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
            ),
          ),
          
          const SizedBox(height: 30),
          
          const Text(
            'تحضير الألغاز الحركية...',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 10),
          
          Text(
            'جاري تحميل ${MotionPuzzleManager.getTotalPuzzleCount()} لغز تفاعلي',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameScreen() {
    if (showResults) {
      return _buildResultsScreen();
    }
    
    return Column(
      children: [
        // Game header
        _buildGameHeader(),
        
        // Main puzzle area
        Expanded(
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: currentPuzzle != null
                  ? MotionPuzzleWidget(
                      puzzle: currentPuzzle!,
                      onComplete: _onPuzzleComplete,
                      onTimeUp: _onTimeUp,
                    )
                  : const Center(
                      child: Text(
                        'لا توجد ألغاز متاحة',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                        ),
                      ),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGameHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => _showExitDialog(),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          
          const SizedBox(width: 10),
          
          // Game info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الألغاز الحركية',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'اللغز ${puzzlesSolved + 1} من ${MotionPuzzleManager.getTotalPuzzleCount()}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          
          // Score display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.amber,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 20),
                const SizedBox(width: 5),
                Text(
                  '$totalScore',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsScreen() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Success icon
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.emoji_events,
                color: Colors.white,
                size: 40,
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'أحسنت!',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            
            const SizedBox(height: 10),
            
            Text(
              'لقد حللت $puzzlesSolved لغز حركي',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Score display
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                children: [
                  const Text(
                    'النقاط الإجمالية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    '$totalScore',
                    style: const TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                    child: const Text('العودة'),
                  ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _restartGame,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                    child: const Text('لعب مرة أخرى'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _onPuzzleComplete(int score) {
    setState(() {
      totalScore += score;
      puzzlesSolved++;
    });

    // Add to advanced game system
    AdvancedGameSystem.addScore(score, true);

    // Play success sounds and effects
    SoundSystem.playCorrectSound();
    SoundSystem.playLevelCompleteSound();
    HapticSystem.mediumImpact();

    // Show completion dialog
    _showCompletionDialog(score);
  }

  void _onTimeUp() {
    SoundSystem.playTimeUpSound();
    HapticSystem.heavyImpact();
    
    _showTimeUpDialog();
  }

  void _showCompletionDialog(int score) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 ممتاز!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('لقد حصلت على $score نقطة!'),
            const SizedBox(height: 10),
            const Text('هل تريد الانتقال للغز التالي؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showResults();
            },
            child: const Text('إنهاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadNextPuzzle();
            },
            child: const Text('التالي'),
          ),
        ],
      ),
    );
  }

  void _showTimeUpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('⏰ انتهى الوقت!'),
        content: const Text('لم تتمكن من حل اللغز في الوقت المحدد.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showResults();
            },
            child: const Text('إنهاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadNextPuzzle();
            },
            child: const Text('المحاولة مرة أخرى'),
          ),
        ],
      ),
    );
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنهاء اللعبة؟'),
        content: const Text('هل أنت متأكد من أنك تريد إنهاء اللعبة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('إنهاء'),
          ),
        ],
      ),
    );
  }

  void _loadNextPuzzle() {
    _transitionController.reset();
    
    setState(() {
      currentPuzzle = MotionPuzzleManager.getNextPuzzle();
    });
    
    _transitionController.forward();
  }

  void _showResults() {
    setState(() {
      showResults = true;
    });
  }

  void _restartGame() {
    setState(() {
      totalScore = 0;
      puzzlesSolved = 0;
      showResults = false;
      currentPuzzle = MotionPuzzleManager.getCurrentPuzzle();
    });
    
    _transitionController.reset();
    _transitionController.forward();
  }
}
