import 'package:flutter/material.dart';
import 'sound_system.dart';
import 'save_system.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool soundEnabled = true;
  bool musicEnabled = true;
  double soundVolume = 1.0;
  double musicVolume = 0.5;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    Map<String, dynamic> settings = await SaveSystem.loadGameSettings();
    setState(() {
      soundEnabled = settings['soundEnabled'] ?? true;
      musicEnabled = settings['musicEnabled'] ?? true;
      soundVolume = settings['soundVolume'] ?? 1.0;
      musicVolume = settings['musicVolume'] ?? 0.5;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE9ECEF),
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(20),
          children: [
            // قسم الأصوات
            _buildSectionCard(
              title: 'الأصوات والموسيقى',
              icon: Icons.volume_up,
              children: [
                _buildSwitchTile(
                  title: 'تأثيرات صوتية',
                  subtitle: 'أصوات الإجابات والنقر',
                  value: soundEnabled,
                  onChanged: (value) async {
                    setState(() {
                      soundEnabled = value;
                    });
                    await SoundSystem.setSoundEnabled(value);
                    if (value) SoundSystem.playClickSound();
                  },
                ),
                
                if (soundEnabled)
                  _buildSliderTile(
                    title: 'مستوى الأصوات',
                    value: soundVolume,
                    onChanged: (value) async {
                      setState(() {
                        soundVolume = value;
                      });
                      await SoundSystem.setSoundVolume(value);
                    },
                  ),
                
                _buildSwitchTile(
                  title: 'موسيقى خلفية',
                  subtitle: 'موسيقى أثناء اللعب',
                  value: musicEnabled,
                  onChanged: (value) async {
                    setState(() {
                      musicEnabled = value;
                    });
                    await SoundSystem.setMusicEnabled(value);
                  },
                ),
                
                if (musicEnabled)
                  _buildSliderTile(
                    title: 'مستوى الموسيقى',
                    value: musicVolume,
                    onChanged: (value) async {
                      setState(() {
                        musicVolume = value;
                      });
                      await SoundSystem.setMusicVolume(value);
                    },
                  ),
              ],
            ),

            const SizedBox(height: 20),

            // قسم اللعبة
            _buildSectionCard(
              title: 'إعدادات اللعبة',
              icon: Icons.games,
              children: [
                _buildActionTile(
                  title: 'مسح البيانات',
                  subtitle: 'حذف جميع التقدم والنقاط',
                  icon: Icons.delete_forever,
                  color: Colors.red,
                  onTap: () => _showClearDataDialog(),
                ),
                
                _buildActionTile(
                  title: 'استعادة الإعدادات الافتراضية',
                  subtitle: 'إعادة تعيين جميع الإعدادات',
                  icon: Icons.restore,
                  color: Colors.orange,
                  onTap: () => _showResetSettingsDialog(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // قسم المعلومات
            _buildSectionCard(
              title: 'حول اللعبة',
              icon: Icons.info,
              children: [
                _buildInfoTile(
                  title: 'الإصدار',
                  value: '1.0.0',
                ),
                
                _buildInfoTile(
                  title: 'عدد الألغاز',
                  value: '7,400+',
                ),
                
                _buildInfoTile(
                  title: 'المطور',
                  value: 'Brain Test Team',
                ),
                
                _buildActionTile(
                  title: 'تقييم اللعبة',
                  subtitle: 'ساعدنا بتقييمك',
                  icon: Icons.star,
                  color: Colors.amber,
                  onTap: () => _showRatingDialog(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.deepPurple, size: 24),
                const SizedBox(width: 10),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.deepPurple,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSliderTile({
    required String title,
    required double value,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Slider(
          value: value,
          onChanged: onChanged,
          activeColor: Colors.deepPurple,
          divisions: 10,
          label: '${(value * 100).round()}%',
        ),
      ],
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        SoundSystem.playClickSound();
        HapticSystem.lightImpact();
        onTap();
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح البيانات'),
        content: const Text(
          'هل أنت متأكد من أنك تريد حذف جميع البيانات؟\n'
          'سيتم فقدان جميع النقاط والتقدم والإنجازات.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await SaveSystem.clearAllData();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح جميع البيانات')),
              );
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة الإعدادات'),
        content: const Text('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await SaveSystem.saveGameSettings(
                soundEnabled: true,
                musicEnabled: true,
                soundVolume: 1.0,
                musicVolume: 0.5,
              );
              await _loadSettings();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم استعادة الإعدادات الافتراضية')),
              );
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقييم اللعبة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('كيف تقيم تجربتك مع اللعبة؟'),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () {
                    SoundSystem.playClickSound();
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('شكراً لتقييمك ${index + 1} نجوم!')),
                    );
                  },
                  icon: const Icon(Icons.star, color: Colors.amber, size: 30),
                );
              }),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
