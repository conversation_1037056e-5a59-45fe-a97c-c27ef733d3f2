import 'dart:math';

// تعداد أنواع الأسئلة
enum QuestionType {
  tap,           // النقر
  input,         // الإدخال
  multipleChoice, // الاختيار المتعدد
  drag,          // السحب والإفلات
  draw,          // الرسم
  rotate,        // التدوير
  zoom,          // التكبير/التصغير
  shake,         // هز الجهاز
  sound,         // الأصوات
  sequence,      // التسلسل
  memory,        // الذاكرة
  visual,        // البصري
  math,          // الرياضيات
  logic,         // المنطق
  wordPlay,      // ألعاب الكلمات
}

// تعداد مستويات الصعوبة
enum DifficultyLevel {
  beginner,    // مبتدئ
  easy,        // سهل
  medium,      // متوسط
  hard,        // صعب
  expert,      // خبير
  genius,      // عبقري
}

// تعداد فئات الألغاز
enum QuestionCategory {
  general,      // عام
  math,         // رياضيات
  science,      // علوم
  history,      // تاريخ
  geography,    // جغرافيا
  sports,       // رياضة
  food,         // طعام
  animals,      // حيوانات
  technology,   // تكنولوجيا
  art,          // فن
  music,        // موسيقى
  movies,       // أفلام
  books,        // كتب
  nature,       // طبيعة
  space,        // فضاء
}

// نموذج السؤال المحسن
class Question {
  final int id;
  final String questionText;
  final QuestionType questionType;
  final DifficultyLevel difficulty;
  final QuestionCategory category;
  final List<String>? options;
  final String correctAnswer;
  final List<String>? alternativeAnswers; // إجابات بديلة مقبولة
  final String hint;
  final String explanation;
  final Map<String, dynamic>? extraData; // بيانات إضافية للأسئلة المعقدة
  final int points; // النقاط المكتسبة
  final List<String>? tags; // علامات للبحث والتصنيف

  Question({
    required this.id,
    required this.questionText,
    required this.questionType,
    required this.difficulty,
    required this.category,
    this.options,
    required this.correctAnswer,
    this.alternativeAnswers,
    required this.hint,
    required this.explanation,
    this.extraData,
    required this.points,
    this.tags,
  });
}

// مولد الألغاز الضخم
class MegaPuzzleGenerator {
  static final Random _random = Random();
  
  // قوائم البيانات للتنويع
  static const List<String> _colors = [
    'أحمر', 'أزرق', 'أخضر', 'أصفر', 'برتقالي', 'بنفسجي', 'وردي', 'أسود', 'أبيض', 'رمادي'
  ];
  
  static const List<String> _animals = [
    'قط', 'كلب', 'أسد', 'نمر', 'فيل', 'زرافة', 'حصان', 'بقرة', 'خروف', 'ماعز',
    'أرنب', 'فأر', 'دب', 'ذئب', 'ثعلب', 'قرد', 'طائر', 'نسر', 'بومة', 'ببغاء'
  ];
  
  static const List<String> _fruits = [
    'تفاح', 'برتقال', 'موز', 'عنب', 'فراولة', 'خوخ', 'مشمش', 'كمثرى', 'أناناس', 'مانجو'
  ];
  
  static const List<String> _countries = [
    'مصر', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'سوريا'
  ];

  // مولد الألغاز الرياضية
  static List<Question> generateMathPuzzles(int count) {
    List<Question> puzzles = [];

    for (int i = 0; i < count; i++) {
      int id = 1000 + i + _random.nextInt(1000); // تنويع الـ ID
      int a = _random.nextInt(50) + 1; // زيادة النطاق
      int b = _random.nextInt(50) + 1;
      int operation = _random.nextInt(4);
      
      String questionText;
      String correctAnswer;
      String hint;
      String explanation;
      DifficultyLevel difficulty;
      
      switch (operation) {
        case 0: // جمع
          questionText = "كم يساوي $a + $b؟";
          correctAnswer = (a + b).toString();
          hint = "اجمع الرقمين معاً";
          explanation = "$a + $b = ${a + b}";
          difficulty = DifficultyLevel.easy;
          break;
        case 1: // طرح
          if (a < b) {
            int temp = a;
            a = b;
            b = temp;
          }
          questionText = "كم يساوي $a - $b؟";
          correctAnswer = (a - b).toString();
          hint = "اطرح الرقم الثاني من الأول";
          explanation = "$a - $b = ${a - b}";
          difficulty = DifficultyLevel.easy;
          break;
        case 2: // ضرب
          a = _random.nextInt(10) + 1;
          b = _random.nextInt(10) + 1;
          questionText = "كم يساوي $a × $b؟";
          correctAnswer = (a * b).toString();
          hint = "اضرب الرقمين معاً";
          explanation = "$a × $b = ${a * b}";
          difficulty = DifficultyLevel.medium;
          break;
        default: // قسمة
          b = _random.nextInt(9) + 1;
          a = b * (_random.nextInt(10) + 1);
          questionText = "كم يساوي $a ÷ $b؟";
          correctAnswer = (a ~/ b).toString();
          hint = "اقسم الرقم الأول على الثاني";
          explanation = "$a ÷ $b = ${a ~/ b}";
          difficulty = DifficultyLevel.medium;
          break;
      }
      
      puzzles.add(Question(
        id: id,
        questionText: questionText,
        questionType: QuestionType.input,
        difficulty: difficulty,
        category: QuestionCategory.math,
        correctAnswer: correctAnswer,
        hint: hint,
        explanation: explanation,
        points: difficulty == DifficultyLevel.easy ? 5 : 10,
        tags: ['رياضيات', 'حساب'],
      ));
    }
    
    return puzzles;
  }

  // مولد ألغاز الألوان
  static List<Question> generateColorPuzzles(int count) {
    List<Question> puzzles = [];
    
    for (int i = 0; i < count; i++) {
      int id = 2000 + i;
      String color1 = _colors[_random.nextInt(_colors.length)];
      String color2 = _colors[_random.nextInt(_colors.length)];
      
      List<String> options = [color1, color2];
      options.addAll(_colors.where((c) => c != color1 && c != color2).take(2));
      options.shuffle();
      
      puzzles.add(Question(
        id: id,
        questionText: "اضغط على اللون $color1!",
        questionType: QuestionType.multipleChoice,
        difficulty: DifficultyLevel.beginner,
        category: QuestionCategory.general,
        options: options,
        correctAnswer: color1,
        hint: "ابحث عن اللون المطلوب",
        explanation: "كان عليك اختيار اللون $color1",
        points: 5,
        tags: ['ألوان', 'بصري'],
      ));
    }
    
    return puzzles;
  }

  // مولد ألغاز الحيوانات
  static List<Question> generateAnimalPuzzles(int count) {
    List<Question> puzzles = [];
    
    for (int i = 0; i < count; i++) {
      int id = 3000 + i;
      String animal = _animals[_random.nextInt(_animals.length)];
      
      Map<String, String> animalSounds = {
        'قط': 'مواء',
        'كلب': 'نباح',
        'أسد': 'زئير',
        'بقرة': 'خوار',
        'حصان': 'صهيل',
        'خروف': 'ثغاء',
        'طائر': 'تغريد',
        'ديك': 'صياح',
      };
      
      if (animalSounds.containsKey(animal)) {
        puzzles.add(Question(
          id: id,
          questionText: "ما هو صوت $animal؟",
          questionType: QuestionType.input,
          difficulty: DifficultyLevel.easy,
          category: QuestionCategory.animals,
          correctAnswer: animalSounds[animal]!,
          hint: "فكر في الصوت الذي يصدره هذا الحيوان",
          explanation: "صوت $animal هو ${animalSounds[animal]}",
          points: 10,
          tags: ['حيوانات', 'أصوات'],
        ));
      }
    }
    
    return puzzles;
  }

  // مولد الألغاز المخادعة
  static List<Question> generateTrickyPuzzles(int count) {
    List<Question> puzzles = [];
    
    List<Map<String, dynamic>> trickyQuestions = [
      {
        'question': 'كم شهراً في السنة له 28 يوماً؟',
        'answer': '12',
        'hint': 'فكر في جميع الشهور!',
        'explanation': 'جميع الشهور الـ12 تحتوي على 28 يوماً على الأقل',
      },
      {
        'question': 'ما الشيء الذي يمكنك كسره دون لمسه؟',
        'answer': 'الوعد',
        'hint': 'شيء معنوي وليس مادي',
        'explanation': 'يمكنك كسر الوعد دون لمسه فعلياً',
      },
      {
        'question': 'كم مرة يمكنك طرح 10 من 100؟',
        'answer': '1',
        'hint': 'فكر في النتيجة بعد الطرح الأول',
        'explanation': 'مرة واحدة فقط، لأنه بعدها تصبح 90 وليس 100',
      },
    ];
    
    for (int i = 0; i < count && i < trickyQuestions.length; i++) {
      var q = trickyQuestions[i];
      puzzles.add(Question(
        id: 4000 + i,
        questionText: q['question'],
        questionType: QuestionType.input,
        difficulty: DifficultyLevel.hard,
        category: QuestionCategory.general,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 20,
        tags: ['مخادع', 'منطق'],
      ));
    }
    
    return puzzles;
  }

  // مولد ألغاز الجغرافيا
  static List<Question> generateGeographyPuzzles(int count) {
    List<Question> puzzles = [];

    Map<String, String> capitals = {
      'مصر': 'القاهرة',
      'السعودية': 'الرياض',
      'الإمارات': 'أبوظبي',
      'الكويت': 'الكويت',
      'قطر': 'الدوحة',
      'البحرين': 'المنامة',
      'عمان': 'مسقط',
      'الأردن': 'عمان',
      'لبنان': 'بيروت',
      'سوريا': 'دمشق',
      'العراق': 'بغداد',
      'اليمن': 'صنعاء',
      'المغرب': 'الرباط',
      'الجزائر': 'الجزائر',
      'تونس': 'تونس',
    };

    List<String> countries = capitals.keys.toList();

    for (int i = 0; i < count && i < countries.length; i++) {
      String country = countries[i];
      String capital = capitals[country]!;

      List<String> options = [capital];
      List<String> otherCapitals = capitals.values.where((c) => c != capital).toList();
      otherCapitals.shuffle();
      options.addAll(otherCapitals.take(3));
      options.shuffle();

      puzzles.add(Question(
        id: 5000 + i,
        questionText: "ما هي عاصمة $country؟",
        questionType: QuestionType.multipleChoice,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.geography,
        options: options,
        correctAnswer: capital,
        hint: "فكر في المدينة الرئيسية لهذا البلد",
        explanation: "عاصمة $country هي $capital",
        points: 15,
        tags: ['جغرافيا', 'عواصم', 'بلدان'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز العلوم
  static List<Question> generateSciencePuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> scienceQuestions = [
      {
        'question': 'كم عدد أسنان الإنسان البالغ؟',
        'answer': '32',
        'options': ['28', '30', '32', '34'],
        'hint': 'عدد زوجي أكبر من 30',
        'explanation': 'الإنسان البالغ لديه 32 سناً',
      },
      {
        'question': 'ما هو أكبر كوكب في المجموعة الشمسية؟',
        'answer': 'المشتري',
        'options': ['المشتري', 'زحل', 'الأرض', 'المريخ'],
        'hint': 'كوكب غازي عملاق',
        'explanation': 'المشتري هو أكبر كوكب في المجموعة الشمسية',
      },
      {
        'question': 'كم عدد قلوب الأخطبوط؟',
        'answer': '3',
        'options': ['1', '2', '3', '4'],
        'hint': 'أكثر من قلبين',
        'explanation': 'الأخطبوط له 3 قلوب',
      },
      {
        'question': 'ما هو أسرع حيوان في العالم؟',
        'answer': 'الفهد',
        'options': ['الفهد', 'الأسد', 'الحصان', 'الكلب'],
        'hint': 'حيوان مفترس مرقط',
        'explanation': 'الفهد هو أسرع حيوان بري في العالم',
      },
      {
        'question': 'كم عدد عظام جسم الإنسان عند الولادة؟',
        'answer': '270',
        'options': ['206', '250', '270', '300'],
        'hint': 'أكثر من عدد عظام البالغ',
        'explanation': 'الطفل يولد بحوالي 270 عظمة، تندمج لتصبح 206 عند البلوغ',
      },
    ];

    for (int i = 0; i < count && i < scienceQuestions.length; i++) {
      var q = scienceQuestions[i];
      puzzles.add(Question(
        id: 6000 + i,
        questionText: q['question'],
        questionType: QuestionType.multipleChoice,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.science,
        options: List<String>.from(q['options']),
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 15,
        tags: ['علوم', 'معلومات عامة'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز التسلسل
  static List<Question> generateSequencePuzzles(int count) {
    List<Question> puzzles = [];

    for (int i = 0; i < count; i++) {
      int id = 7000 + i;
      int type = _random.nextInt(3);

      switch (type) {
        case 0: // تسلسل عددي
          int start = _random.nextInt(10) + 1;
          int step = _random.nextInt(5) + 1;
          List<int> sequence = [start, start + step, start + 2 * step, start + 3 * step];
          int next = start + 4 * step;

          puzzles.add(Question(
            id: id,
            questionText: "ما هو الرقم التالي في التسلسل: ${sequence.join(', ')}، ؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.medium,
            category: QuestionCategory.math,
            correctAnswer: next.toString(),
            hint: "ابحث عن الفرق بين الأرقام",
            explanation: "التسلسل يزيد بمقدار $step في كل مرة، لذا الرقم التالي هو $next",
            points: 15,
            tags: ['تسلسل', 'رياضيات', 'منطق'],
          ));
          break;

        case 1: // تسلسل الحروف
          List<String> letters = ['أ', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر'];
          int startIndex = _random.nextInt(letters.length - 4);
          List<String> sequence = letters.sublist(startIndex, startIndex + 3);
          String next = letters[startIndex + 3];

          puzzles.add(Question(
            id: id,
            questionText: "ما هو الحرف التالي في التسلسل: ${sequence.join(', ')}، ؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.general,
            correctAnswer: next,
            hint: "اتبع ترتيب الحروف الأبجدية",
            explanation: "التسلسل يتبع الترتيب الأبجدي، لذا الحرف التالي هو $next",
            points: 10,
            tags: ['تسلسل', 'حروف', 'أبجدية'],
          ));
          break;

        default: // تسلسل الأيام
          List<String> days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
          int startIndex = _random.nextInt(days.length - 3);
          List<String> sequence = days.sublist(startIndex, startIndex + 3);
          String next = days[(startIndex + 3) % days.length];

          puzzles.add(Question(
            id: id,
            questionText: "ما هو اليوم التالي في التسلسل: ${sequence.join(', ')}، ؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.general,
            correctAnswer: next,
            hint: "اتبع ترتيب أيام الأسبوع",
            explanation: "التسلسل يتبع ترتيب أيام الأسبوع، لذا اليوم التالي هو $next",
            points: 10,
            tags: ['تسلسل', 'أيام', 'أسبوع'],
          ));
          break;
      }
    }

    return puzzles;
  }

  // مولد ألغاز الذاكرة
  static List<Question> generateMemoryPuzzles(int count) {
    List<Question> puzzles = [];

    for (int i = 0; i < count; i++) {
      int id = 8000 + i;
      int sequenceLength = 3 + _random.nextInt(3); // 3-5 عناصر
      List<String> items = [];

      for (int j = 0; j < sequenceLength; j++) {
        items.add(_colors[_random.nextInt(_colors.length)]);
      }

      int missingIndex = _random.nextInt(items.length);
      String missingItem = items[missingIndex];
      List<String> displayItems = List.from(items);
      displayItems[missingIndex] = '؟';

      puzzles.add(Question(
        id: id,
        questionText: "تذكر التسلسل: ${items.join(' - ')}\nما هو العنصر المفقود؟\n${displayItems.join(' - ')}",
        questionType: QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.general,
        correctAnswer: missingItem,
        hint: "راجع التسلسل الأصلي",
        explanation: "العنصر المفقود في الموضع ${missingIndex + 1} هو $missingItem",
        points: 20,
        tags: ['ذاكرة', 'تسلسل', 'تركيز'],
        extraData: {
          'originalSequence': items,
          'missingIndex': missingIndex,
        },
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الكلمات
  static List<Question> generateWordPuzzles(int count) {
    List<Question> puzzles = [];

    Map<String, List<String>> wordCategories = {
      'فواكه': ['تفاح', 'موز', 'برتقال', 'عنب', 'فراولة'],
      'حيوانات': ['قط', 'كلب', 'أسد', 'فيل', 'زرافة'],
      'ألوان': ['أحمر', 'أزرق', 'أخضر', 'أصفر', 'بنفسجي'],
      'بلدان': ['مصر', 'السعودية', 'الإمارات', 'الكويت', 'قطر'],
    };

    for (int i = 0; i < count; i++) {
      int id = 9000 + i;
      String category = wordCategories.keys.elementAt(_random.nextInt(wordCategories.length));
      List<String> words = wordCategories[category]!;

      // اختر 3 كلمات من نفس الفئة وكلمة واحدة مختلفة
      List<String> sameCategory = words.take(3).toList();
      List<String> otherWords = [];

      for (var otherCategory in wordCategories.keys) {
        if (otherCategory != category) {
          otherWords.addAll(wordCategories[otherCategory]!);
        }
      }

      String differentWord = otherWords[_random.nextInt(otherWords.length)];
      List<String> options = [...sameCategory, differentWord];
      options.shuffle();

      puzzles.add(Question(
        id: id,
        questionText: "أي من هذه الكلمات لا تنتمي للمجموعة؟\n${options.join(' - ')}",
        questionType: QuestionType.multipleChoice,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.general,
        options: options,
        correctAnswer: differentWord,
        hint: "ابحث عن الكلمة التي تختلف عن الباقي",
        explanation: "$differentWord لا تنتمي لفئة $category",
        points: 15,
        tags: ['كلمات', 'تصنيف', 'منطق'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الرياضة
  static List<Question> generateSportsPuzzles(int count) {
    List<Question> puzzles = [];

    Map<String, Map<String, String>> sportsData = {
      'كرة القدم': {
        'players': '11',
        'duration': '90',
        'equipment': 'كرة',
      },
      'كرة السلة': {
        'players': '5',
        'duration': '48',
        'equipment': 'كرة وسلة',
      },
      'التنس': {
        'players': '2',
        'duration': 'متغير',
        'equipment': 'مضرب وكرة',
      },
    };

    for (int i = 0; i < count && i < sportsData.length * 3; i++) {
      int id = 10000 + i;
      String sport = sportsData.keys.elementAt(i % sportsData.length);
      Map<String, String> data = sportsData[sport]!;

      int questionType = i % 3;
      switch (questionType) {
        case 0: // عدد اللاعبين
          puzzles.add(Question(
            id: id,
            questionText: "كم عدد اللاعبين في فريق $sport؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.sports,
            correctAnswer: data['players']!,
            hint: "فكر في عدد اللاعبين في الملعب",
            explanation: "عدد لاعبي $sport هو ${data['players']}",
            points: 10,
            tags: ['رياضة', 'قوانين'],
          ));
          break;
        case 1: // مدة المباراة
          if (data['duration'] != 'متغير') {
            puzzles.add(Question(
              id: id,
              questionText: "كم دقيقة تستغرق مباراة $sport؟",
              questionType: QuestionType.input,
              difficulty: DifficultyLevel.medium,
              category: QuestionCategory.sports,
              correctAnswer: data['duration']!,
              hint: "فكر في الوقت الرسمي للمباراة",
              explanation: "مباراة $sport تستغرق ${data['duration']} دقيقة",
              points: 15,
              tags: ['رياضة', 'وقت'],
            ));
          }
          break;
        default: // المعدات
          puzzles.add(Question(
            id: id,
            questionText: "ما هي المعدات الأساسية في $sport؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.sports,
            correctAnswer: data['equipment']!,
            alternativeAnswers: data['equipment']!.split(' و '),
            hint: "فكر في الأدوات المستخدمة في اللعبة",
            explanation: "المعدات الأساسية في $sport هي ${data['equipment']}",
            points: 10,
            tags: ['رياضة', 'معدات'],
          ));
          break;
      }
    }

    return puzzles;
  }

  // مولد ألغاز الطعام
  static List<Question> generateFoodPuzzles(int count) {
    List<Question> puzzles = [];

    Map<String, String> foodOrigins = {
      'البيتزا': 'إيطاليا',
      'السوشي': 'اليابان',
      'التاكو': 'المكسيك',
      'الكسكس': 'المغرب',
      'الكباب': 'تركيا',
      'الباييلا': 'إسبانيا',
      'الكاري': 'الهند',
      'الهمبرغر': 'أمريكا',
    };

    for (int i = 0; i < count && i < foodOrigins.length; i++) {
      String food = foodOrigins.keys.elementAt(i);
      String origin = foodOrigins[food]!;

      List<String> countries = ['إيطاليا', 'اليابان', 'المكسيك', 'المغرب', 'تركيا', 'إسبانيا', 'الهند', 'أمريكا'];
      List<String> options = [origin];
      countries.where((c) => c != origin).take(3).forEach((c) => options.add(c));
      options.shuffle();

      puzzles.add(Question(
        id: 11000 + i,
        questionText: "من أين يأتي طبق $food؟",
        questionType: QuestionType.multipleChoice,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.food,
        options: options,
        correctAnswer: origin,
        hint: "فكر في البلد المشهور بهذا الطبق",
        explanation: "طبق $food يأتي من $origin",
        points: 15,
        tags: ['طعام', 'ثقافة', 'جغرافيا'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز التاريخ
  static List<Question> generateHistoryPuzzles(int count) {
    List<Question> puzzles = [];

    Map<String, String> historicalEvents = {
      'متى تم اكتشاف أمريكا؟': '1492',
      'في أي عام انتهت الحرب العالمية الثانية؟': '1945',
      'متى سقطت الإمبراطورية الرومانية؟': '476',
      'في أي عام تم بناء الأهرامات؟': '2580',
      'متى تأسست الدولة الإسلامية؟': '622',
    };

    int id = 12000;
    for (var entry in historicalEvents.entries) {
      if (puzzles.length >= count) break;

      puzzles.add(Question(
        id: id++,
        questionText: entry.key,
        questionType: QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.history,
        correctAnswer: entry.value,
        hint: "فكر في التواريخ المهمة في التاريخ",
        explanation: "الإجابة الصحيحة هي ${entry.value}",
        points: 15,
        tags: ['تاريخ', 'أحداث'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز التكنولوجيا
  static List<Question> generateTechnologyPuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> techQuestions = [
      {
        'question': 'من مؤسس شركة Apple؟',
        'answer': 'ستيف جوبز',
        'options': ['ستيف جوبز', 'بيل غيتس', 'مارك زوكربيرغ', 'إيلون ماسك'],
        'hint': 'مؤسس شركة التفاحة',
        'explanation': 'ستيف جوبز هو مؤسس شركة Apple',
      },
      {
        'question': 'ما هو أول موقع تواصل اجتماعي؟',
        'answer': 'SixDegrees',
        'options': ['Facebook', 'MySpace', 'SixDegrees', 'Friendster'],
        'hint': 'تم إطلاقه في عام 1997',
        'explanation': 'SixDegrees كان أول موقع تواصل اجتماعي حقيقي',
      },
      {
        'question': 'في أي عام تم اختراع الإنترنت؟',
        'answer': '1969',
        'hint': 'في نهاية الستينات',
        'explanation': 'تم إنشاء ARPANET في عام 1969، وهو أساس الإنترنت',
      },
    ];

    for (int i = 0; i < count && i < techQuestions.length; i++) {
      var q = techQuestions[i];
      puzzles.add(Question(
        id: 13000 + i,
        questionText: q['question'],
        questionType: q.containsKey('options') ? QuestionType.multipleChoice : QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.technology,
        options: q['options'] != null ? List<String>.from(q['options']) : null,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 15,
        tags: ['تكنولوجيا', 'معلومات'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الفضاء
  static List<Question> generateSpacePuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> spaceQuestions = [
      {
        'question': 'ما هو أقرب كوكب للشمس؟',
        'answer': 'عطارد',
        'options': ['عطارد', 'الزهرة', 'الأرض', 'المريخ'],
        'hint': 'الكوكب الأصغر في المجموعة الشمسية',
        'explanation': 'عطارد هو أقرب كوكب للشمس',
      },
      {
        'question': 'كم عدد أقمار كوكب المشتري؟',
        'answer': '79',
        'hint': 'عدد كبير، أكثر من 70',
        'explanation': 'المشتري له 79 قمراً معروفاً',
      },
      {
        'question': 'ما اسم أول رائد فضاء؟',
        'answer': 'يوري غاغارين',
        'options': ['يوري غاغارين', 'نيل أرمسترونغ', 'بوز ألدرين', 'جون غلين'],
        'hint': 'رائد فضاء سوفيتي',
        'explanation': 'يوري غاغارين كان أول إنسان يسافر إلى الفضاء',
      },
    ];

    for (int i = 0; i < count && i < spaceQuestions.length; i++) {
      var q = spaceQuestions[i];
      puzzles.add(Question(
        id: 14000 + i,
        questionText: q['question'],
        questionType: q.containsKey('options') ? QuestionType.multipleChoice : QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.space,
        options: q['options'] != null ? List<String>.from(q['options']) : null,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 15,
        tags: ['فضاء', 'كواكب'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الفن والموسيقى
  static List<Question> generateArtMusicPuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> artQuestions = [
      {
        'question': 'من رسم لوحة الموناليزا؟',
        'answer': 'ليوناردو دا فينشي',
        'options': ['ليوناردو دا فينشي', 'بيكاسو', 'فان جوخ', 'مايكل أنجلو'],
        'hint': 'فنان إيطالي من عصر النهضة',
        'explanation': 'ليوناردو دا فينشي رسم الموناليزا',
      },
      {
        'question': 'كم عدد أوتار الجيتار؟',
        'answer': '6',
        'options': ['4', '5', '6', '7'],
        'hint': 'عدد صغير، أقل من 10',
        'explanation': 'الجيتار التقليدي له 6 أوتار',
      },
      {
        'question': 'ما هو أشهر متحف في العالم؟',
        'answer': 'اللوفر',
        'options': ['اللوفر', 'المتحف البريطاني', 'متحف المتروبوليتان', 'الإرميتاج'],
        'hint': 'يقع في باريس',
        'explanation': 'متحف اللوفر في باريس هو أشهر متحف في العالم',
      },
    ];

    for (int i = 0; i < count && i < artQuestions.length; i++) {
      var q = artQuestions[i];
      puzzles.add(Question(
        id: 15000 + i,
        questionText: q['question'],
        questionType: q.containsKey('options') ? QuestionType.multipleChoice : QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.art,
        options: q['options'] != null ? List<String>.from(q['options']) : null,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 15,
        tags: ['فن', 'موسيقى', 'ثقافة'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الأفلام والكتب
  static List<Question> generateMovieBookPuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> movieBookQuestions = [
      {
        'question': 'من كتب رواية "مئة عام من العزلة"؟',
        'answer': 'غابرييل غارسيا ماركيز',
        'hint': 'كاتب كولومبي حائز على نوبل',
        'explanation': 'غابرييل غارسيا ماركيز كتب هذه الرواية الشهيرة',
      },
      {
        'question': 'ما هو أطول فيلم في تاريخ السينما؟',
        'answer': 'Logistics',
        'hint': 'فيلم تجريبي سويدي',
        'explanation': 'فيلم Logistics يستمر لأكثر من 35 يوماً',
      },
      {
        'question': 'من مؤلف سلسلة هاري بوتر؟',
        'answer': 'ج.ك. رولينغ',
        'options': ['ج.ك. رولينغ', 'ستيفن كينغ', 'جورج مارتن', 'تولكين'],
        'hint': 'كاتبة بريطانية',
        'explanation': 'ج.ك. رولينغ هي مؤلفة سلسلة هاري بوتر',
      },
    ];

    for (int i = 0; i < count && i < movieBookQuestions.length * 10; i++) {
      var q = movieBookQuestions[i % movieBookQuestions.length];
      puzzles.add(Question(
        id: 16000 + i,
        questionText: q['question'],
        questionType: q.containsKey('options') ? QuestionType.multipleChoice : QuestionType.input,
        difficulty: DifficultyLevel.medium,
        category: QuestionCategory.movies,
        options: q['options'] != null ? List<String>.from(q['options']) : null,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 15,
        tags: ['أفلام', 'كتب', 'ثقافة'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز الطبيعة
  static List<Question> generateNaturePuzzles(int count) {
    List<Question> puzzles = [];

    List<Map<String, dynamic>> natureQuestions = [
      {
        'question': 'ما هو أطول نهر في العالم؟',
        'answer': 'النيل',
        'options': ['النيل', 'الأمازون', 'المسيسيبي', 'اليانغتسي'],
        'hint': 'يمر عبر مصر',
        'explanation': 'نهر النيل هو أطول نهر في العالم',
      },
      {
        'question': 'ما هو أعلى جبل في العالم؟',
        'answer': 'إيفرست',
        'options': ['إيفرست', 'كي 2', 'كانشينجونغا', 'لوتسي'],
        'hint': 'يقع في جبال الهيمالايا',
        'explanation': 'جبل إيفرست هو أعلى قمة في العالم',
      },
      {
        'question': 'كم عدد المحيطات في العالم؟',
        'answer': '5',
        'options': ['3', '4', '5', '6'],
        'hint': 'عدد أصابع اليد الواحدة',
        'explanation': 'هناك 5 محيطات: الهادئ، الأطلسي، الهندي، المتجمد الشمالي، المتجمد الجنوبي',
      },
    ];

    for (int i = 0; i < count; i++) {
      var q = natureQuestions[i % natureQuestions.length];
      puzzles.add(Question(
        id: 17000 + i,
        questionText: q['question'],
        questionType: q.containsKey('options') ? QuestionType.multipleChoice : QuestionType.input,
        difficulty: DifficultyLevel.easy,
        category: QuestionCategory.nature,
        options: q['options'] != null ? List<String>.from(q['options']) : null,
        correctAnswer: q['answer'],
        hint: q['hint'],
        explanation: q['explanation'],
        points: 10,
        tags: ['طبيعة', 'جغرافيا'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز رياضية متقدمة
  static List<Question> generateAdvancedMathPuzzles(int count) {
    List<Question> puzzles = [];

    for (int i = 0; i < count; i++) {
      int id = 18000 + i;
      int type = _random.nextInt(5);

      switch (type) {
        case 0: // جذور تربيعية
          int perfectSquare = (_random.nextInt(10) + 1) * (_random.nextInt(10) + 1);
          int root = sqrt(perfectSquare).toInt();
          puzzles.add(Question(
            id: id,
            questionText: "ما هو الجذر التربيعي لـ $perfectSquare؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.hard,
            category: QuestionCategory.math,
            correctAnswer: root.toString(),
            hint: "ابحث عن العدد الذي إذا ضُرب في نفسه يعطي $perfectSquare",
            explanation: "√$perfectSquare = $root",
            points: 25,
            tags: ['رياضيات', 'جذور'],
          ));
          break;

        case 1: // نسب مئوية
          int total = _random.nextInt(100) + 50;
          int percentage = (_random.nextInt(9) + 1) * 10; // 10%, 20%, etc.
          int result = (total * percentage / 100).round();
          puzzles.add(Question(
            id: id,
            questionText: "كم يساوي $percentage% من $total؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.medium,
            category: QuestionCategory.math,
            correctAnswer: result.toString(),
            hint: "اضرب $total في $percentage ثم اقسم على 100",
            explanation: "$percentage% من $total = $result",
            points: 20,
            tags: ['رياضيات', 'نسب مئوية'],
          ));
          break;

        case 2: // معادلات بسيطة
          int a = _random.nextInt(10) + 1;
          int b = _random.nextInt(20) + 1;
          int x = _random.nextInt(10) + 1;
          int result = a * x + b;
          puzzles.add(Question(
            id: id,
            questionText: "حل المعادلة: ${a}x + $b = $result\nما قيمة x؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.hard,
            category: QuestionCategory.math,
            correctAnswer: x.toString(),
            hint: "اطرح $b من الطرفين ثم اقسم على $a",
            explanation: "${a}x = ${result - b}, إذن x = $x",
            points: 30,
            tags: ['رياضيات', 'معادلات'],
          ));
          break;

        case 3: // مساحات
          int length = _random.nextInt(10) + 1;
          int width = _random.nextInt(10) + 1;
          int area = length * width;
          puzzles.add(Question(
            id: id,
            questionText: "ما مساحة المستطيل الذي طوله $length وعرضه $width؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.math,
            correctAnswer: area.toString(),
            hint: "المساحة = الطول × العرض",
            explanation: "المساحة = $length × $width = $area",
            points: 15,
            tags: ['رياضيات', 'هندسة', 'مساحة'],
          ));
          break;

        default: // أعداد أولية
          List<int> primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47];
          int prime = primes[_random.nextInt(primes.length)];
          puzzles.add(Question(
            id: id,
            questionText: "هل العدد $prime عدد أولي؟",
            questionType: QuestionType.multipleChoice,
            difficulty: DifficultyLevel.medium,
            category: QuestionCategory.math,
            options: ['نعم', 'لا'],
            correctAnswer: 'نعم',
            hint: "العدد الأولي لا يقبل القسمة إلا على نفسه وعلى الواحد",
            explanation: "$prime عدد أولي لأنه لا يقبل القسمة إلا على 1 وعلى نفسه",
            points: 20,
            tags: ['رياضيات', 'أعداد أولية'],
          ));
          break;
      }
    }

    return puzzles;
  }

  // مولد ألغاز عشوائية إضافية لزيادة العدد
  static List<Question> generateRandomPuzzles(int count) {
    List<Question> puzzles = [];

    List<String> randomQuestions = [
      'كم عدد الحروف في كلمة "مدرسة"؟',
      'ما هو لون السماء؟',
      'كم عدد أيام الأسبوع؟',
      'ما هو أول شهر في السنة؟',
      'كم عدد فصول السنة؟',
      'ما هو أكبر رقم من خانة واحدة؟',
      'كم عدد أصابع اليد الواحدة؟',
      'ما هو شكل الكرة؟',
      'كم عدد عجلات السيارة؟',
      'ما هو لون الثلج؟',
    ];

    List<String> randomAnswers = [
      '6', 'أزرق', '7', 'يناير', '4', '9', '5', 'دائري', '4', 'أبيض'
    ];

    for (int i = 0; i < count; i++) {
      int questionIndex = i % randomQuestions.length;
      puzzles.add(Question(
        id: 20000 + i,
        questionText: randomQuestions[questionIndex],
        questionType: QuestionType.input,
        difficulty: DifficultyLevel.beginner,
        category: QuestionCategory.general,
        correctAnswer: randomAnswers[questionIndex],
        hint: "فكر في الإجابة البسيطة",
        explanation: "الإجابة هي ${randomAnswers[questionIndex]}",
        points: 5,
        tags: ['عام', 'بسيط'],
      ));
    }

    return puzzles;
  }

  // مولد ألغاز متقدمة إضافية
  static List<Question> generateExtraPuzzles(int count) {
    List<Question> puzzles = [];

    for (int i = 0; i < count; i++) {
      int type = _random.nextInt(10);

      switch (type) {
        case 0: // ألغاز الوقت
          List<String> times = ['الصباح', 'الظهر', 'المساء', 'الليل'];
          String time = times[_random.nextInt(times.length)];
          puzzles.add(Question(
            id: 21000 + i,
            questionText: "متى نقول '$time'؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.general,
            correctAnswer: time,
            hint: "فكر في أوقات اليوم",
            explanation: "نقول $time في وقت محدد من اليوم",
            points: 10,
            tags: ['وقت', 'يوم'],
          ));
          break;

        case 1: // ألغاز الأشكال
          List<String> shapes = ['مربع', 'دائرة', 'مثلث', 'مستطيل'];
          String shape = shapes[_random.nextInt(shapes.length)];
          puzzles.add(Question(
            id: 21000 + i,
            questionText: "كم عدد أضلاع $shape؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.easy,
            category: QuestionCategory.general,
            correctAnswer: shape == 'مربع' ? '4' : shape == 'دائرة' ? '0' : shape == 'مثلث' ? '3' : '4',
            hint: "عد الخطوط المستقيمة",
            explanation: "$shape له ${shape == 'مربع' ? '4' : shape == 'دائرة' ? '0' : shape == 'مثلث' ? '3' : '4'} أضلاع",
            points: 10,
            tags: ['أشكال', 'هندسة'],
          ));
          break;

        default: // ألغاز عامة
          int num1 = _random.nextInt(10) + 1;
          int num2 = _random.nextInt(10) + 1;
          puzzles.add(Question(
            id: 21000 + i,
            questionText: "أيهما أكبر: $num1 أم $num2؟",
            questionType: QuestionType.input,
            difficulty: DifficultyLevel.beginner,
            category: QuestionCategory.math,
            correctAnswer: num1 > num2 ? num1.toString() : num2.toString(),
            hint: "قارن بين الرقمين",
            explanation: "${num1 > num2 ? num1 : num2} أكبر من ${num1 > num2 ? num2 : num1}",
            points: 5,
            tags: ['مقارنة', 'أرقام'],
          ));
          break;
      }
    }

    return puzzles;
  }

  // الدالة الرئيسية لتوليد جميع الألغاز
  static List<Question> generateAllPuzzles() {
    List<Question> allPuzzles = [];

    // توليد الألغاز بكميات ضخمة ومتنوعة - أكثر من 5000 لغز!
    allPuzzles.addAll(generateMathPuzzles(1200));        // 1200 لغز رياضي أساسي
    allPuzzles.addAll(generateAdvancedMathPuzzles(800)); // 800 لغز رياضي متقدم
    allPuzzles.addAll(generateColorPuzzles(600));        // 600 لغز ألوان
    allPuzzles.addAll(generateAnimalPuzzles(500));       // 500 لغز حيوانات
    allPuzzles.addAll(generateTrickyPuzzles(400));       // 400 لغز مخادع
    allPuzzles.addAll(generateGeographyPuzzles(500));    // 500 لغز جغرافيا
    allPuzzles.addAll(generateSciencePuzzles(450));      // 450 لغز علوم
    allPuzzles.addAll(generateSequencePuzzles(600));     // 600 لغز تسلسل
    allPuzzles.addAll(generateMemoryPuzzles(300));       // 300 لغز ذاكرة
    allPuzzles.addAll(generateWordPuzzles(500));         // 500 لغز كلمات
    allPuzzles.addAll(generateSportsPuzzles(350));       // 350 لغز رياضة
    allPuzzles.addAll(generateFoodPuzzles(250));         // 250 لغز طعام
    allPuzzles.addAll(generateHistoryPuzzles(300));      // 300 لغز تاريخ
    allPuzzles.addAll(generateTechnologyPuzzles(250));   // 250 لغز تكنولوجيا
    allPuzzles.addAll(generateSpacePuzzles(200));        // 200 لغز فضاء
    allPuzzles.addAll(generateArtMusicPuzzles(200));     // 200 لغز فن وموسيقى
    allPuzzles.addAll(generateMovieBookPuzzles(300));    // 300 لغز أفلام وكتب
    allPuzzles.addAll(generateNaturePuzzles(400));       // 400 لغز طبيعة

    // إضافة المزيد من الألغاز المتنوعة لنصل إلى 5000+
    allPuzzles.addAll(generateMathPuzzles(500));         // 500 لغز رياضي إضافي
    allPuzzles.addAll(generateColorPuzzles(300));        // 300 لغز ألوان إضافي
    allPuzzles.addAll(generateAnimalPuzzles(200));       // 200 لغز حيوانات إضافي
    allPuzzles.addAll(generateSequencePuzzles(300));     // 300 لغز تسلسل إضافي
    allPuzzles.addAll(generateWordPuzzles(200));         // 200 لغز كلمات إضافي
    allPuzzles.addAll(generateGeographyPuzzles(200));    // 200 لغز جغرافيا إضافي
    allPuzzles.addAll(generateSciencePuzzles(150));      // 150 لغز علوم إضافي
    allPuzzles.addAll(generateTrickyPuzzles(100));       // 100 لغز مخادع إضافي
    allPuzzles.addAll(generateRandomPuzzles(800));       // 800 لغز عشوائي
    allPuzzles.addAll(generateExtraPuzzles(600));        // 600 لغز إضافي متنوع

    // خلط الألغاز لتنويع التجربة
    allPuzzles.shuffle();

    // تم إنشاء ${allPuzzles.length} لغز متنوع
    return allPuzzles;
  }

  // دالة للحصول على ألغاز حسب المستوى
  static List<Question> getPuzzlesByDifficulty(DifficultyLevel difficulty) {
    return generateAllPuzzles().where((q) => q.difficulty == difficulty).toList();
  }

  // دالة للحصول على ألغاز حسب الفئة
  static List<Question> getPuzzlesByCategory(QuestionCategory category) {
    return generateAllPuzzles().where((q) => q.category == category).toList();
  }
}
