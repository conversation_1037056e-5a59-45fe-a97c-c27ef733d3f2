import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'save_system.dart';

class SoundSystem {
  static bool _soundEnabled = true;
  static bool _musicEnabled = true;
  static double _soundVolume = 1.0;
  static double _musicVolume = 0.5;

  // تهيئة نظام الأصوات
  static Future<void> initialize() async {
    try {
      Map<String, dynamic> settings = await SaveSystem.loadGameSettings();
      _soundEnabled = settings['soundEnabled'] ?? true;
      _musicEnabled = settings['musicEnabled'] ?? true;
      _soundVolume = settings['soundVolume'] ?? 1.0;
      _musicVolume = settings['musicVolume'] ?? 0.5;
    } catch (e) {
      debugPrint('خطأ في تهيئة نظام الأصوات: $e');
    }
  }

  // تشغيل صوت الإجابة الصحيحة
  static Future<void> playCorrectSound() async {
    if (!_soundEnabled) return;

    try {
      // تسلسل أصوات للنجاح
      await SystemSound.play(SystemSoundType.click);
      await Future.delayed(const Duration(milliseconds: 100));
      await SystemSound.play(SystemSoundType.click);
      await HapticFeedback.mediumImpact();
      debugPrint('تم تشغيل صوت النجاح');
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت الإجابة الصحيحة: $e');
    }
  }

  // تشغيل صوت الإجابة الخاطئة
  static Future<void> playWrongSound() async {
    if (!_soundEnabled) return;

    try {
      // اهتزاز قوي للخطأ
      await HapticFeedback.heavyImpact();
      await Future.delayed(const Duration(milliseconds: 100));
      await HapticFeedback.heavyImpact();
      debugPrint('تم تشغيل صوت الخطأ');
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت الإجابة الخاطئة: $e');
    }
  }

  // تشغيل صوت انتهاء الوقت
  static Future<void> playTimeUpSound() async {
    if (!_soundEnabled) return;

    try {
      // تسلسل اهتزازات للتحذير
      for (int i = 0; i < 3; i++) {
        await HapticFeedback.heavyImpact();
        await Future.delayed(const Duration(milliseconds: 200));
      }
      debugPrint('تم تشغيل صوت انتهاء الوقت');
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت انتهاء الوقت: $e');
    }
  }

  // تشغيل صوت زر
  static Future<void> playButtonSound() async {
    if (!_soundEnabled) return;

    try {
      await SystemSound.play(SystemSoundType.click);
      await HapticFeedback.selectionClick();
      debugPrint('تم تشغيل صوت الزر');
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت الزر: $e');
    }
  }

  // تشغيل صوت النقر
  static Future<void> playClickSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت النقر: $e');
    }
  }

  // تشغيل صوت إكمال المرحلة
  static Future<void> playLevelCompleteSound() async {
    if (!_soundEnabled) return;
    
    try {
      // تشغيل سلسلة أصوات للاحتفال
      await SystemSound.play(SystemSoundType.click);
      await Future.delayed(const Duration(milliseconds: 200));
      await SystemSound.play(SystemSoundType.click);
      await Future.delayed(const Duration(milliseconds: 200));
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت إكمال المرحلة: $e');
    }
  }

  // تشغيل صوت فتح مرحلة جديدة
  static Future<void> playUnlockSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت فتح المرحلة: $e');
    }
  }

  // تفعيل/إلغاء تفعيل الأصوات
  static Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
  }

  // تفعيل/إلغاء تفعيل الموسيقى
  static Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    await _saveSettings();
  }

  // تعيين مستوى صوت التأثيرات
  static Future<void> setSoundVolume(double volume) async {
    _soundVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  // تعيين مستوى صوت الموسيقى
  static Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  // حفظ الإعدادات
  static Future<void> _saveSettings() async {
    await SaveSystem.saveGameSettings(
      soundEnabled: _soundEnabled,
      musicEnabled: _musicEnabled,
      soundVolume: _soundVolume,
      musicVolume: _musicVolume,
    );
  }

  // الحصول على حالة الأصوات
  static bool get soundEnabled => _soundEnabled;
  static bool get musicEnabled => _musicEnabled;
  static double get soundVolume => _soundVolume;
  static double get musicVolume => _musicVolume;
}

// نظام الاهتزاز للتأثيرات اللمسية
class HapticSystem {
  // اهتزاز خفيف للنقر
  static Future<void> lightImpact() async {
    try {
      await HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('خطأ في الاهتزاز الخفيف: $e');
    }
  }

  // اهتزاز متوسط للإجابة الصحيحة
  static Future<void> mediumImpact() async {
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      debugPrint('خطأ في الاهتزاز المتوسط: $e');
    }
  }

  // اهتزاز قوي للإجابة الخاطئة
  static Future<void> heavyImpact() async {
    try {
      await HapticFeedback.heavyImpact();
    } catch (e) {
      debugPrint('خطأ في الاهتزاز القوي: $e');
    }
  }

  // اهتزاز للتحديد
  static Future<void> selectionClick() async {
    try {
      await HapticFeedback.selectionClick();
    } catch (e) {
      debugPrint('خطأ في اهتزاز التحديد: $e');
    }
  }
}

// نظام التأثيرات البصرية المتقدمة
class VisualEffectsSystem {
  // إنشاء تأثير الجسيمات للإجابة الصحيحة
  static Widget createSuccessParticles() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: ParticlesPainter(
          particles: _generateSuccessParticles(),
          color: Colors.green,
        ),
      ),
    );
  }

  // إنشاء تأثير الجسيمات للإجابة الخاطئة
  static Widget createErrorParticles() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: ParticlesPainter(
          particles: _generateErrorParticles(),
          color: Colors.red,
        ),
      ),
    );
  }

  // توليد جسيمات النجاح
  static List<Particle> _generateSuccessParticles() {
    List<Particle> particles = [];
    for (int i = 0; i < 20; i++) {
      particles.add(Particle(
        x: 200 + (i * 10).toDouble(),
        y: 200 + (i * 5).toDouble(),
        size: 5.0 + (i % 3),
        speed: 2.0 + (i % 2),
      ));
    }
    return particles;
  }

  // توليد جسيمات الخطأ
  static List<Particle> _generateErrorParticles() {
    List<Particle> particles = [];
    for (int i = 0; i < 15; i++) {
      particles.add(Particle(
        x: 200 + (i * 15).toDouble(),
        y: 200 + (i * 8).toDouble(),
        size: 3.0 + (i % 2),
        speed: 1.5 + (i % 3),
      ));
    }
    return particles;
  }
}

// نموذج الجسيم
class Particle {
  final double x;
  final double y;
  final double size;
  final double speed;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
  });
}

// رسام الجسيمات
class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final Color color;

  ParticlesPainter({
    required this.particles,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;

    for (var particle in particles) {
      canvas.drawCircle(
        Offset(particle.x, particle.y),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// نظام الإشعارات داخل اللعبة
class NotificationSystem {
  static OverlayEntry? _currentNotification;

  // إظهار إشعار نجاح
  static void showSuccessNotification(BuildContext context, String message) {
    _showNotification(
      context,
      message,
      Colors.green,
      Icons.check_circle,
    );
  }

  // إظهار إشعار خطأ
  static void showErrorNotification(BuildContext context, String message) {
    _showNotification(
      context,
      message,
      Colors.red,
      Icons.error,
    );
  }

  // إظهار إشعار معلومات
  static void showInfoNotification(BuildContext context, String message) {
    _showNotification(
      context,
      message,
      Colors.blue,
      Icons.info,
    );
  }

  // إظهار إشعار إنجاز
  static void showAchievementNotification(BuildContext context, String achievement) {
    _showNotification(
      context,
      'إنجاز جديد: $achievement',
      Colors.amber,
      Icons.emoji_events,
    );
  }

  // إظهار إشعار عام
  static void _showNotification(
    BuildContext context,
    String message,
    Color color,
    IconData icon,
  ) {
    // إزالة الإشعار السابق إن وجد
    _currentNotification?.remove();

    _currentNotification = OverlayEntry(
      builder: (context) => Positioned(
        top: 100,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_currentNotification!);

    // إزالة الإشعار بعد 3 ثوانٍ
    Timer(const Duration(seconds: 3), () {
      _currentNotification?.remove();
      _currentNotification = null;
    });
  }
}


