import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:async';
import 'motion_puzzle_system.dart';
import 'sound_system.dart';

// واجهة اللغز الحركي الرئيسية
class MotionPuzzleWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int score) onComplete;
  final VoidCallback onTimeUp;

  const MotionPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onComplete,
    required this.onTimeUp,
  });

  @override
  State<MotionPuzzleWidget> createState() => _MotionPuzzleWidgetState();
}

class _MotionPuzzleWidgetState extends State<MotionPuzzleWidget>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late Timer _gameTimer;
  
  int timeLeft = 0;
  int currentScore = 0;
  bool isCompleted = false;
  bool showHint = false;
  int currentHintIndex = 0;

  @override
  void initState() {
    super.initState();
    timeLeft = widget.puzzle.timeLimit;
    
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
    
    _startTimer();
    _mainController.forward();
  }

  void _startTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && !isCompleted) {
        setState(() {
          timeLeft--;
          if (timeLeft <= 0) {
            timer.cancel();
            widget.onTimeUp();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _gameTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            widget.puzzle.primaryColor.withValues(alpha: 0.8),
            widget.puzzle.secondaryColor.withValues(alpha: 0.6),
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Header with timer and score
          _buildHeader(),
          
          // Main puzzle area
          Expanded(
            child: _buildPuzzleArea(),
          ),
          
          // Controls and hints
          _buildControls(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: timeLeft <= 10 ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.timer, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  '$timeLeft',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Puzzle title
          Text(
            widget.puzzle.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          // Score
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.amber,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  '$currentScore',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPuzzleArea() {
    switch (widget.puzzle.type) {
      case MotionPuzzleType.dragAndDrop:
        return DragDropPuzzleWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.gesturePattern:
        return GesturePatternWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.physicsSimulation:
        return PhysicsSimulationWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.colorMatching:
        return ColorMatchingWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.shapeManipulation:
        return ShapeManipulationWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.gravityPuzzle:
        return GravityPuzzleWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.magneticField:
        return MagneticFieldWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.fluidDynamics:
        return FluidDynamicsWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.particleSystem:
        return ParticleSystemWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.aiInteraction:
        return AIInteractionWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      default:
        return const Center(child: Text('نوع اللغز غير مدعوم'));
    }
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Hint button
          ElevatedButton.icon(
            onPressed: _showNextHint,
            icon: const Icon(Icons.lightbulb),
            label: const Text('تلميح'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
          ),
          
          // Reset button
          ElevatedButton.icon(
            onPressed: _resetPuzzle,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              foregroundColor: Colors.white,
            ),
          ),
          
          // Skip button (costs points)
          ElevatedButton.icon(
            onPressed: _skipPuzzle,
            icon: const Icon(Icons.skip_next),
            label: const Text('تخطي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _updateScore(int points) {
    setState(() {
      currentScore += points;
    });
    SoundSystem.playCorrectSound();
    HapticSystem.mediumImpact();
  }

  void _completePuzzle() {
    if (!isCompleted) {
      setState(() {
        isCompleted = true;
      });
      _gameTimer.cancel();
      
      // Bonus for remaining time
      int timeBonus = timeLeft * 2;
      currentScore += timeBonus;
      
      SoundSystem.playLevelCompleteSound();
      HapticSystem.mediumImpact();
      
      widget.onComplete(currentScore);
    }
  }

  void _showNextHint() {
    if (widget.puzzle.hints.isNotEmpty) {
      setState(() {
        showHint = true;
        currentHintIndex = (currentHintIndex + 1) % widget.puzzle.hints.length;
      });
      
      SoundSystem.playClickSound();
      
      // Hide hint after 3 seconds
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            showHint = false;
          });
        }
      });
    }
  }

  void _resetPuzzle() {
    SoundSystem.playClickSound();
    // Reset puzzle state - implementation depends on puzzle type
  }

  void _skipPuzzle() {
    SoundSystem.playClickSound();
    // Deduct points for skipping
    currentScore = max(0, currentScore - 50);
    _completePuzzle();
  }
}

// Widget للألغاز المختلفة - مبسط وفعال
class DragDropPuzzleWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const DragDropPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<DragDropPuzzleWidget> createState() => _DragDropPuzzleWidgetState();
}

class _DragDropPuzzleWidgetState extends State<DragDropPuzzleWidget> {
  List<Color> ballColors = [Colors.red, Colors.blue, Colors.green];
  List<bool> completed = [false, false, false];
  int completedCount = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 40),

          // Draggable balls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: ballColors.asMap().entries.map((entry) {
              int index = entry.key;
              Color color = entry.value;

              if (completed[index]) {
                return Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey, width: 2),
                  ),
                  child: const Icon(Icons.check, color: Colors.grey),
                );
              }

              return Draggable<int>(
                data: index,
                feedback: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                ),
                childWhenDragging: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                    border: Border.all(color: color, width: 2),
                  ),
                ),
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 80),

          // Drop targets
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: ballColors.asMap().entries.map((entry) {
              int index = entry.key;
              Color color = entry.value;

              return DragTarget<int>(
                onAcceptWithDetails: (details) {
                  if (details.data == index && !completed[index]) {
                    setState(() {
                      completed[index] = true;
                      completedCount++;
                    });

                    widget.onScoreUpdate(30);
                    SoundSystem.playCorrectSound();

                    if (completedCount >= ballColors.length) {
                      Future.delayed(const Duration(milliseconds: 500), () {
                        widget.onComplete();
                      });
                    }
                  }
                },
                builder: (context, candidateData, rejectedData) {
                  return Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: completed[index]
                          ? color
                          : color.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: candidateData.isNotEmpty ? Colors.white : Colors.grey,
                        width: 4,
                      ),
                    ),
                    child: completed[index]
                        ? const Icon(Icons.check, color: Colors.white, size: 30)
                        : Icon(Icons.circle, color: color, size: 40),
                  );
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 40),

          // Progress indicator
          LinearProgressIndicator(
            value: completedCount / ballColors.length,
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(widget.puzzle.primaryColor),
          ),
        ],
      ),
    );
  }
}

// واجهة لغز الإيماءات
class GesturePatternWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const GesturePatternWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<GesturePatternWidget> createState() => _GesturePatternWidgetState();
}

class _GesturePatternWidgetState extends State<GesturePatternWidget> {
  bool isDrawing = false;
  List<Offset> drawnPath = [];
  bool isCompleted = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // Drawing area
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey, width: 2),
              ),
              child: GestureDetector(
                onPanStart: (details) {
                  setState(() {
                    isDrawing = true;
                    drawnPath.clear();
                    drawnPath.add(details.localPosition);
                  });
                },
                onPanUpdate: (details) {
                  if (isDrawing) {
                    setState(() {
                      drawnPath.add(details.localPosition);
                    });
                  }
                },
                onPanEnd: (details) {
                  setState(() {
                    isDrawing = false;
                  });
                  _checkPattern();
                },
                child: CustomPaint(
                  painter: PathPainter(drawnPath),
                  child: Container(),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Clear button
          ElevatedButton(
            onPressed: () {
              setState(() {
                drawnPath.clear();
                isCompleted = false;
              });
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _checkPattern() {
    // Simple circle detection
    if (drawnPath.length > 20) {
      double totalDistance = 0;
      Offset center = _calculateCenter();

      for (Offset point in drawnPath) {
        totalDistance += (point - center).distance;
      }

      double averageDistance = totalDistance / drawnPath.length;
      double variance = 0;

      for (Offset point in drawnPath) {
        double distance = (point - center).distance;
        variance += (distance - averageDistance) * (distance - averageDistance);
      }

      variance /= drawnPath.length;

      // If variance is low, it's likely a circle
      if (variance < 1000 && !isCompleted) {
        setState(() {
          isCompleted = true;
        });
        widget.onScoreUpdate(50);
        SoundSystem.playCorrectSound();
        Future.delayed(const Duration(milliseconds: 500), () {
          widget.onComplete();
        });
      }
    }
  }

  Offset _calculateCenter() {
    double x = 0, y = 0;
    for (Offset point in drawnPath) {
      x += point.dx;
      y += point.dy;
    }
    return Offset(x / drawnPath.length, y / drawnPath.length);
  }
}

// رسام المسار
class PathPainter extends CustomPainter {
  final List<Offset> path;

  PathPainter(this.path);

  @override
  void paint(Canvas canvas, Size size) {
    if (path.length < 2) return;

    Paint paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 5.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    Path drawPath = Path();
    drawPath.moveTo(path[0].dx, path[0].dy);

    for (int i = 1; i < path.length; i++) {
      drawPath.lineTo(path[i].dx, path[i].dy);
    }

    canvas.drawPath(drawPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// واجهة لغز الفيزياء
class PhysicsSimulationWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const PhysicsSimulationWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<PhysicsSimulationWidget> createState() => _PhysicsSimulationWidgetState();
}

class _PhysicsSimulationWidgetState extends State<PhysicsSimulationWidget>
    with TickerProviderStateMixin {
  late AnimationController _ballController;
  late Animation<Offset> _ballAnimation;
  bool ballReleased = false;
  bool targetHit = false;

  @override
  void initState() {
    super.initState();
    _ballController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _ballAnimation = Tween<Offset>(
      begin: const Offset(0.1, 0.1),
      end: const Offset(0.8, 0.8),
    ).animate(CurvedAnimation(
      parent: _ballController,
      curve: Curves.bounceOut,
    ));

    _ballController.addStatusListener((status) {
      if (status == AnimationStatus.completed && !targetHit) {
        setState(() {
          targetHit = true;
        });
        widget.onScoreUpdate(40);
        SoundSystem.playCorrectSound();
        Future.delayed(const Duration(milliseconds: 500), () {
          widget.onComplete();
        });
      }
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // Physics area
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey, width: 2),
              ),
              child: Stack(
                children: [
                  // Target
                  const Positioned(
                    right: 50,
                    bottom: 50,
                    child: CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.green,
                      child: Icon(Icons.flag, color: Colors.white, size: 30),
                    ),
                  ),

                  // Ball
                  AnimatedBuilder(
                    animation: _ballAnimation,
                    builder: (context, child) {
                      return Positioned(
                        left: _ballAnimation.value.dx * MediaQuery.of(context).size.width * 0.7,
                        top: _ballAnimation.value.dy * 300,
                        child: GestureDetector(
                          onTap: () {
                            if (!ballReleased) {
                              setState(() {
                                ballReleased = true;
                              });
                              _ballController.forward();
                            }
                          },
                          child: CircleAvatar(
                            radius: 20,
                            backgroundColor: targetHit ? Colors.green : Colors.red,
                            child: Icon(
                              targetHit ? Icons.check : Icons.sports_basketball,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Release button
          if (!ballReleased)
            ElevatedButton(
              onPressed: () {
                setState(() {
                  ballReleased = true;
                });
                _ballController.forward();
              },
              child: const Text('إطلاق الكرة'),
            ),
        ],
      ),
    );
  }
}

// نماذج البيانات
class DraggableItem {
  final int id;
  final Color color;
  final Offset position;

  DraggableItem({
    required this.id,
    required this.color,
    required this.position,
  });
}

class DropTarget {
  final int id;
  final Color color;
  final Offset position;
  bool isCompleted;

  DropTarget({
    required this.id,
    required this.color,
    required this.position,
    required this.isCompleted,
  });
}

// Placeholder widgets للألغاز الأخرى

class ColorMatchingWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ColorMatchingWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<ColorMatchingWidget> createState() => _ColorMatchingWidgetState();
}

class _ColorMatchingWidgetState extends State<ColorMatchingWidget> {
  Color? selectedColor1;
  Color? selectedColor2;
  Color? resultColor;
  bool isCompleted = false;

  final List<Color> availableColors = [
    Colors.red,
    Colors.blue,
    Colors.yellow,
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 30),

          // Color selection
          const Text(
            'اختر لونين لخلطهما:',
            style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 20),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: availableColors.map((color) {
              bool isSelected = selectedColor1 == color || selectedColor2 == color;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (selectedColor1 == null) {
                      selectedColor1 = color;
                    } else if (selectedColor2 == null && selectedColor1 != color) {
                      selectedColor2 = color;
                      _mixColors();
                    } else {
                      // Reset selection
                      selectedColor1 = color;
                      selectedColor2 = null;
                      resultColor = null;
                    }
                  });
                },
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.grey,
                      width: isSelected ? 4 : 2,
                    ),
                  ),
                  child: isSelected ? const Icon(Icons.check, color: Colors.white) : null,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 40),

          // Mixing area
          if (selectedColor1 != null && selectedColor2 != null)
            Column(
              children: [
                const Text(
                  'النتيجة:',
                  style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: resultColor ?? Colors.grey,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                  ),
                  child: isCompleted ? const Icon(Icons.star, color: Colors.white, size: 40) : null,
                ),
              ],
            ),

          const SizedBox(height: 30),

          // Reset button
          ElevatedButton(
            onPressed: () {
              setState(() {
                selectedColor1 = null;
                selectedColor2 = null;
                resultColor = null;
                isCompleted = false;
              });
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _mixColors() {
    if (selectedColor1 != null && selectedColor2 != null) {
      // Simple color mixing logic
      if ((selectedColor1 == Colors.red && selectedColor2 == Colors.blue) ||
          (selectedColor1 == Colors.blue && selectedColor2 == Colors.red)) {
        resultColor = Colors.purple;
      } else if ((selectedColor1 == Colors.red && selectedColor2 == Colors.yellow) ||
                 (selectedColor1 == Colors.yellow && selectedColor2 == Colors.red)) {
        resultColor = Colors.orange;
      } else if ((selectedColor1 == Colors.blue && selectedColor2 == Colors.yellow) ||
                 (selectedColor1 == Colors.yellow && selectedColor2 == Colors.blue)) {
        resultColor = Colors.green;
      }

      if (resultColor != null && !isCompleted) {
        setState(() {
          isCompleted = true;
        });
        widget.onScoreUpdate(40);
        SoundSystem.playCorrectSound();
        Future.delayed(const Duration(milliseconds: 1000), () {
          widget.onComplete();
        });
      }
    }
  }
}

class ShapeManipulationWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ShapeManipulationWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<ShapeManipulationWidget> createState() => _ShapeManipulationWidgetState();
}

class _ShapeManipulationWidgetState extends State<ShapeManipulationWidget>
    with TickerProviderStateMixin {
  late AnimationController _transformController;
  late Animation<double> _transformAnimation;
  bool isTransforming = false;
  bool isCompleted = false;
  int tapCount = 0;
  final int requiredTaps = 5;

  @override
  void initState() {
    super.initState();
    _transformController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _transformAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _transformController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _transformController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 30),

          Text(
            'اضغط على الشكل $requiredTaps مرات لتحويله',
            style: const TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 20),

          Text(
            'الضغطات: $tapCount / $requiredTaps',
            style: const TextStyle(fontSize: 14, color: Colors.white70),
          ),

          const SizedBox(height: 40),

          // Shape transformation area
          Expanded(
            child: Center(
              child: GestureDetector(
                onTap: () {
                  if (!isCompleted) {
                    setState(() {
                      tapCount++;
                      isTransforming = true;
                    });

                    _transformController.forward().then((_) {
                      _transformController.reverse();
                      setState(() {
                        isTransforming = false;
                      });
                    });

                    SoundSystem.playClickSound();

                    if (tapCount >= requiredTaps) {
                      setState(() {
                        isCompleted = true;
                      });
                      widget.onScoreUpdate(50);
                      SoundSystem.playCorrectSound();
                      Future.delayed(const Duration(milliseconds: 500), () {
                        widget.onComplete();
                      });
                    }
                  }
                },
                child: AnimatedBuilder(
                  animation: _transformAnimation,
                  builder: (context, child) {
                    double progress = tapCount / requiredTaps;
                    double borderRadius = progress * 50; // Transform from square to circle

                    return Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? Colors.green
                            : Color.lerp(Colors.blue, Colors.orange, progress)!,
                        borderRadius: BorderRadius.circular(borderRadius),
                        border: Border.all(
                          color: Colors.white,
                          width: 3,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      transform: Matrix4.identity()
                        ..scale(1.0 + (_transformAnimation.value * 0.2)),
                      child: Center(
                        child: Icon(
                          isCompleted ? Icons.check : Icons.touch_app,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Progress indicator
          LinearProgressIndicator(
            value: tapCount / requiredTaps,
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(widget.puzzle.primaryColor),
          ),
        ],
      ),
    );
  }
}

class GravityPuzzleWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const GravityPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<GravityPuzzleWidget> createState() => _GravityPuzzleWidgetState();
}

class _GravityPuzzleWidgetState extends State<GravityPuzzleWidget>
    with TickerProviderStateMixin {
  late AnimationController _gravityController;
  late Animation<Offset> _ballPosition;
  bool gravityActivated = false;
  bool isCompleted = false;

  @override
  void initState() {
    super.initState();
    _gravityController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _ballPosition = Tween<Offset>(
      begin: const Offset(0.5, 0.1),
      end: const Offset(0.5, 0.8),
    ).animate(CurvedAnimation(
      parent: _gravityController,
      curve: Curves.bounceOut,
    ));

    _gravityController.addStatusListener((status) {
      if (status == AnimationStatus.completed && !isCompleted) {
        setState(() {
          isCompleted = true;
        });
        widget.onScoreUpdate(35);
        SoundSystem.playCorrectSound();
        Future.delayed(const Duration(milliseconds: 500), () {
          widget.onComplete();
        });
      }
    });
  }

  @override
  void dispose() {
    _gravityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.puzzle.description,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 30),

          // Gravity simulation area
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey, width: 2),
              ),
              child: Stack(
                children: [
                  // Ground
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.brown.withValues(alpha: 0.7),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(18),
                          bottomRight: Radius.circular(18),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          'الأرض',
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),

                  // Ball
                  AnimatedBuilder(
                    animation: _ballPosition,
                    builder: (context, child) {
                      return Positioned(
                        left: _ballPosition.value.dx * MediaQuery.of(context).size.width * 0.8,
                        top: _ballPosition.value.dy * 300,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isCompleted ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            isCompleted ? Icons.check : Icons.sports_basketball,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Gravity button
          if (!gravityActivated)
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  gravityActivated = true;
                });
                _gravityController.forward();
                SoundSystem.playClickSound();
              },
              icon: const Icon(Icons.arrow_downward),
              label: const Text('تفعيل الجاذبية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),

          if (gravityActivated && !isCompleted)
            const Text(
              'الجاذبية نشطة...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
        ],
      ),
    );
  }
}

class MagneticFieldWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const MagneticFieldWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('المجال المغناطيسي - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class FluidDynamicsWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const FluidDynamicsWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('ديناميكا السوائل - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class ParticleSystemWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ParticleSystemWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('نظام الجسيمات - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class AIInteractionWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const AIInteractionWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('تفاعل الذكاء الاصطناعي - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}
