import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:async';
import 'motion_puzzle_system.dart';
import 'sound_system.dart';

// واجهة اللغز الحركي الرئيسية
class MotionPuzzleWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int score) onComplete;
  final VoidCallback onTimeUp;

  const MotionPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onComplete,
    required this.onTimeUp,
  });

  @override
  State<MotionPuzzleWidget> createState() => _MotionPuzzleWidgetState();
}

class _MotionPuzzleWidgetState extends State<MotionPuzzleWidget>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late Timer _gameTimer;
  
  int timeLeft = 0;
  int currentScore = 0;
  bool isCompleted = false;
  bool showHint = false;
  int currentHintIndex = 0;

  @override
  void initState() {
    super.initState();
    timeLeft = widget.puzzle.timeLimit;
    
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
    
    _startTimer();
    _mainController.forward();
  }

  void _startTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && !isCompleted) {
        setState(() {
          timeLeft--;
          if (timeLeft <= 0) {
            timer.cancel();
            widget.onTimeUp();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _gameTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            widget.puzzle.primaryColor.withValues(alpha: 0.8),
            widget.puzzle.secondaryColor.withValues(alpha: 0.6),
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Header with timer and score
          _buildHeader(),
          
          // Main puzzle area
          Expanded(
            child: _buildPuzzleArea(),
          ),
          
          // Controls and hints
          _buildControls(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: timeLeft <= 10 ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.timer, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  '$timeLeft',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Puzzle title
          Text(
            widget.puzzle.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          // Score
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.amber,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  '$currentScore',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPuzzleArea() {
    switch (widget.puzzle.type) {
      case MotionPuzzleType.dragAndDrop:
        return DragDropPuzzleWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.gesturePattern:
        return GesturePatternWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.physicsSimulation:
        return PhysicsSimulationWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.colorMatching:
        return ColorMatchingWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.shapeManipulation:
        return ShapeManipulationWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.gravityPuzzle:
        return GravityPuzzleWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.magneticField:
        return MagneticFieldWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.fluidDynamics:
        return FluidDynamicsWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.particleSystem:
        return ParticleSystemWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      case MotionPuzzleType.aiInteraction:
        return AIInteractionWidget(
          puzzle: widget.puzzle,
          onScoreUpdate: _updateScore,
          onComplete: _completePuzzle,
        );
      default:
        return const Center(child: Text('نوع اللغز غير مدعوم'));
    }
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Hint button
          ElevatedButton.icon(
            onPressed: _showNextHint,
            icon: const Icon(Icons.lightbulb),
            label: const Text('تلميح'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
          ),
          
          // Reset button
          ElevatedButton.icon(
            onPressed: _resetPuzzle,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              foregroundColor: Colors.white,
            ),
          ),
          
          // Skip button (costs points)
          ElevatedButton.icon(
            onPressed: _skipPuzzle,
            icon: const Icon(Icons.skip_next),
            label: const Text('تخطي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _updateScore(int points) {
    setState(() {
      currentScore += points;
    });
    SoundSystem.playCorrectSound();
    HapticSystem.mediumImpact();
  }

  void _completePuzzle() {
    if (!isCompleted) {
      setState(() {
        isCompleted = true;
      });
      _gameTimer.cancel();
      
      // Bonus for remaining time
      int timeBonus = timeLeft * 2;
      currentScore += timeBonus;
      
      SoundSystem.playLevelCompleteSound();
      HapticSystem.mediumImpact();
      
      widget.onComplete(currentScore);
    }
  }

  void _showNextHint() {
    if (widget.puzzle.hints.isNotEmpty) {
      setState(() {
        showHint = true;
        currentHintIndex = (currentHintIndex + 1) % widget.puzzle.hints.length;
      });
      
      SoundSystem.playClickSound();
      
      // Hide hint after 3 seconds
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            showHint = false;
          });
        }
      });
    }
  }

  void _resetPuzzle() {
    SoundSystem.playClickSound();
    // Reset puzzle state - implementation depends on puzzle type
  }

  void _skipPuzzle() {
    SoundSystem.playClickSound();
    // Deduct points for skipping
    currentScore = max(0, currentScore - 50);
    _completePuzzle();
  }
}

// Widget placeholder للألغاز المختلفة
class DragDropPuzzleWidget extends StatefulWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const DragDropPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  State<DragDropPuzzleWidget> createState() => _DragDropPuzzleWidgetState();
}

class _DragDropPuzzleWidgetState extends State<DragDropPuzzleWidget> {
  List<DraggableItem> items = [];
  List<DropTarget> targets = [];
  int completedTargets = 0;

  @override
  void initState() {
    super.initState();
    _initializePuzzle();
  }

  void _initializePuzzle() {
    // Initialize based on puzzle config
    int ballCount = widget.puzzle.config['ballCount'] ?? 6;
    List<String> colors = List<String>.from(widget.puzzle.config['colors'] ?? []);
    
    for (int i = 0; i < ballCount; i++) {
      items.add(DraggableItem(
        id: i,
        color: _getColorFromString(colors[i % colors.length]),
        position: Offset(50.0 + (i * 60), 100),
      ));
      
      targets.add(DropTarget(
        id: i,
        color: _getColorFromString(colors[i % colors.length]),
        position: Offset(50.0 + (i * 60), 300),
        isCompleted: false,
      ));
    }
  }

  Color _getColorFromString(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'red': return Colors.red;
      case 'blue': return Colors.blue;
      case 'green': return Colors.green;
      case 'yellow': return Colors.yellow;
      case 'purple': return Colors.purple;
      case 'orange': return Colors.orange;
      default: return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Draw targets
        ...targets.map((target) => Positioned(
          left: target.position.dx,
          top: target.position.dy,
          child: DragTarget<DraggableItem>(
            onAcceptWithDetails: (details) {
              if (details.data.color == target.color && !target.isCompleted) {
                setState(() {
                  target.isCompleted = true;
                  completedTargets++;
                });
                
                widget.onScoreUpdate(20);
                
                if (completedTargets >= targets.length) {
                  widget.onComplete();
                }
              }
            },
            builder: (context, candidateData, rejectedData) {
              return Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: target.isCompleted 
                      ? target.color 
                      : target.color.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: candidateData.isNotEmpty ? Colors.white : Colors.grey,
                    width: 3,
                  ),
                ),
                child: target.isCompleted 
                    ? const Icon(Icons.check, color: Colors.white)
                    : null,
              );
            },
          ),
        )).toList(),
        
        // Draw draggable items
        ...items.where((item) => !targets[item.id].isCompleted).map((item) => 
          Positioned(
            left: item.position.dx,
            top: item.position.dy,
            child: Draggable<DraggableItem>(
              data: item,
              feedback: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: item.color,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
              ),
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: item.color,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ).toList(),
      ],
    );
  }
}

// نماذج البيانات
class DraggableItem {
  final int id;
  final Color color;
  final Offset position;

  DraggableItem({
    required this.id,
    required this.color,
    required this.position,
  });
}

class DropTarget {
  final int id;
  final Color color;
  final Offset position;
  bool isCompleted;

  DropTarget({
    required this.id,
    required this.color,
    required this.position,
    required this.isCompleted,
  });
}

// Placeholder widgets للألغاز الأخرى
class GesturePatternWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const GesturePatternWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('لغز الإيماءات - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class PhysicsSimulationWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const PhysicsSimulationWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('محاكاة الفيزياء - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class ColorMatchingWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ColorMatchingWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('مطابقة الألوان - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class ShapeManipulationWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ShapeManipulationWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('تلاعب الأشكال - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class GravityPuzzleWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const GravityPuzzleWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('ألغاز الجاذبية - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class MagneticFieldWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const MagneticFieldWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('المجال المغناطيسي - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class FluidDynamicsWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const FluidDynamicsWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('ديناميكا السوائل - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class ParticleSystemWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const ParticleSystemWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('نظام الجسيمات - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}

class AIInteractionWidget extends StatelessWidget {
  final MotionPuzzle puzzle;
  final Function(int) onScoreUpdate;
  final VoidCallback onComplete;

  const AIInteractionWidget({
    super.key,
    required this.puzzle,
    required this.onScoreUpdate,
    required this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('تفاعل الذكاء الاصطناعي - قيد التطوير', 
        style: TextStyle(fontSize: 18, color: Colors.white)),
    );
  }
}
