import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:async';
import 'sound_system.dart';

// نظام الألغاز الحركية المتطور
enum MotionPuzzleType {
  dragAndDrop,      // السحب والإفلات
  gesturePattern,   // أنماط الإيماءات
  physicsSimulation, // محاكاة الفيزياء
  colorMatching,    // مطابقة الألوان
  shapeManipulation, // تلاعب الأشكال
  gravityPuzzle,    // ألغاز الجاذبية
  magneticField,    // المجال المغناطيسي
  fluidDynamics,    // ديناميكا السوائل
  particleSystem,   // نظام الجسيمات
  aiInteraction,    // التفاعل مع الذكاء الاصطناعي
}

enum PuzzleDifficulty {
  beginner,    // مبتدئ
  easy,        // سهل
  medium,      // متوسط
  hard,        // صعب
  expert,      // خبير
  master,      // أستاذ
}

// نموذج اللغز الحركي
class MotionPuzzle {
  final int id;
  final String title;
  final String description;
  final MotionPuzzleType type;
  final PuzzleDifficulty difficulty;
  final int timeLimit;
  final int maxScore;
  final Color primaryColor;
  final Color secondaryColor;
  final Map<String, dynamic> config;
  final List<String> hints;

  MotionPuzzle({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.timeLimit,
    required this.maxScore,
    required this.primaryColor,
    required this.secondaryColor,
    required this.config,
    required this.hints,
  });
}

// مولد الألغاز الحركية
class MotionPuzzleGenerator {
  static List<MotionPuzzle> generateAllPuzzles() {
    List<MotionPuzzle> puzzles = [];
    
    // ألغاز السحب والإفلات
    puzzles.addAll(_generateDragDropPuzzles());
    
    // ألغاز الإيماءات
    puzzles.addAll(_generateGesturePuzzles());
    
    // ألغاز الفيزياء
    puzzles.addAll(_generatePhysicsPuzzles());
    
    // ألغاز مطابقة الألوان
    puzzles.addAll(_generateColorPuzzles());
    
    // ألغاز تلاعب الأشكال
    puzzles.addAll(_generateShapePuzzles());
    
    // ألغاز الجاذبية
    puzzles.addAll(_generateGravityPuzzles());
    
    // ألغاز المجال المغناطيسي
    puzzles.addAll(_generateMagneticPuzzles());
    
    // ألغاز ديناميكا السوائل
    puzzles.addAll(_generateFluidPuzzles());
    
    // ألغاز نظام الجسيمات
    puzzles.addAll(_generateParticlePuzzles());
    
    // ألغاز الذكاء الاصطناعي
    puzzles.addAll(_generateAIPuzzles());
    
    return puzzles;
  }

  // ألغاز السحب والإفلات
  static List<MotionPuzzle> _generateDragDropPuzzles() {
    return [
      MotionPuzzle(
        id: 1,
        title: 'ترتيب الألوان',
        description: 'اسحب الكرات الملونة إلى الصناديق المطابقة',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 60,
        maxScore: 100,
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        config: {
          'ballCount': 6,
          'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
          'targetPositions': 6,
        },
        hints: ['ابحث عن اللون المطابق', 'اسحب بحذر', 'الوقت محدود!'],
      ),
      
      MotionPuzzle(
        id: 2,
        title: 'بناء البرج',
        description: 'اسحب القطع لبناء برج مستقر',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 90,
        maxScore: 150,
        primaryColor: Colors.brown,
        secondaryColor: Colors.orange,
        config: {
          'pieceCount': 8,
          'towerHeight': 5,
          'stabilityRequired': true,
        },
        hints: ['ابدأ بالقطع الكبيرة', 'حافظ على التوازن', 'فكر في الاستقرار'],
      ),
      
      MotionPuzzle(
        id: 3,
        title: 'مسار الكرة',
        description: 'اسحب العوائق لتوجيه الكرة للهدف',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 120,
        maxScore: 200,
        primaryColor: Colors.green,
        secondaryColor: Colors.lightGreen,
        config: {
          'ballSpeed': 2.0,
          'obstacleCount': 5,
          'targetRadius': 30.0,
        },
        hints: ['احسب مسار الكرة', 'استخدم الفيزياء', 'التوقيت مهم'],
      ),
    ];
  }

  // ألغاز الإيماءات
  static List<MotionPuzzle> _generateGesturePuzzles() {
    return [
      MotionPuzzle(
        id: 11,
        title: 'رسم النمط',
        description: 'ارسم النمط المطلوب بإصبعك',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 45,
        maxScore: 100,
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        config: {
          'pattern': 'circle',
          'accuracy': 0.8,
          'strokeWidth': 5.0,
        },
        hints: ['ارسم ببطء', 'حافظ على الدقة', 'اتبع الخط المرشد'],
      ),
      
      MotionPuzzle(
        id: 12,
        title: 'إيماءة السحر',
        description: 'قم بالإيماءة السحرية لفتح الصندوق',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 60,
        maxScore: 150,
        primaryColor: Colors.indigo,
        secondaryColor: Colors.deepPurple,
        config: {
          'gestureSequence': ['swipeUp', 'swipeRight', 'tap', 'longPress'],
          'timing': [1.0, 1.5, 0.5, 2.0],
        },
        hints: ['اتبع التسلسل', 'التوقيت مهم', 'لا تتسرع'],
      ),
    ];
  }

  // ألغاز الفيزياء
  static List<MotionPuzzle> _generatePhysicsPuzzles() {
    return [
      MotionPuzzle(
        id: 21,
        title: 'قانون الجاذبية',
        description: 'استخدم الجاذبية لتوجيه الكرات للأهداف',
        type: MotionPuzzleType.physicsSimulation,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 90,
        maxScore: 200,
        primaryColor: Colors.red,
        secondaryColor: Colors.pink,
        config: {
          'gravity': 9.8,
          'ballCount': 3,
          'targetCount': 3,
          'obstacles': true,
        },
        hints: ['فكر في مسار السقوط', 'استخدم الارتداد', 'الزاوية مهمة'],
      ),
      
      MotionPuzzle(
        id: 22,
        title: 'البندول المتأرجح',
        description: 'اضبط البندول ليصل للهدف',
        type: MotionPuzzleType.physicsSimulation,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 120,
        maxScore: 250,
        primaryColor: Colors.teal,
        secondaryColor: Colors.cyan,
        config: {
          'pendulumLength': 100.0,
          'initialAngle': 45.0,
          'damping': 0.98,
        },
        hints: ['احسب الزخم', 'التوقيت حاسم', 'استخدم الطاقة الحركية'],
      ),
    ];
  }

  // ألغاز مطابقة الألوان
  static List<MotionPuzzle> _generateColorPuzzles() {
    return [
      MotionPuzzle(
        id: 31,
        title: 'خلط الألوان',
        description: 'امزج الألوان للحصول على اللون المطلوب',
        type: MotionPuzzleType.colorMatching,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 60,
        maxScore: 120,
        primaryColor: Colors.yellow,
        secondaryColor: Colors.amber,
        config: {
          'targetColor': 'green',
          'availableColors': ['blue', 'yellow'],
          'mixingRatio': [0.5, 0.5],
        },
        hints: ['أزرق + أصفر = أخضر', 'اضبط النسبة', 'امزج بعناية'],
      ),
    ];
  }

  // ألغاز تلاعب الأشكال
  static List<MotionPuzzle> _generateShapePuzzles() {
    return [
      MotionPuzzle(
        id: 41,
        title: 'تحويل الأشكال',
        description: 'حول المربع إلى دائرة بالحركة',
        type: MotionPuzzleType.shapeManipulation,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 75,
        maxScore: 180,
        primaryColor: Colors.orange,
        secondaryColor: Colors.deepOrange,
        config: {
          'initialShape': 'square',
          'targetShape': 'circle',
          'transformationSteps': 10,
        },
        hints: ['حرك الزوايا', 'اجعلها منحنية', 'تدريجياً'],
      ),
    ];
  }

  // ألغاز الجاذبية
  static List<MotionPuzzle> _generateGravityPuzzles() {
    return [
      MotionPuzzle(
        id: 51,
        title: 'عكس الجاذبية',
        description: 'اقلب الجاذبية لتوجيه الكرات',
        type: MotionPuzzleType.gravityPuzzle,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 100,
        maxScore: 220,
        primaryColor: Colors.deepPurple,
        secondaryColor: Colors.purple,
        config: {
          'gravityDirection': 'down',
          'canFlip': true,
          'ballCount': 4,
        },
        hints: ['اقلب الاتجاه', 'فكر بالعكس', 'استخدم الجدران'],
      ),
    ];
  }

  // ألغاز المجال المغناطيسي
  static List<MotionPuzzle> _generateMagneticPuzzles() {
    return [
      MotionPuzzle(
        id: 61,
        title: 'المغناطيس القوي',
        description: 'استخدم المغناطيس لجذب الكرات المعدنية',
        type: MotionPuzzleType.magneticField,
        difficulty: PuzzleDifficulty.expert,
        timeLimit: 90,
        maxScore: 300,
        primaryColor: Colors.blueGrey,
        secondaryColor: Colors.grey,
        config: {
          'magnetStrength': 5.0,
          'metalBalls': 6,
          'magnetCount': 2,
        },
        hints: ['الأضداد تتجاذب', 'قوة المجال', 'احسب المسافة'],
      ),
    ];
  }

  // ألغاز ديناميكا السوائل
  static List<MotionPuzzle> _generateFluidPuzzles() {
    return [
      MotionPuzzle(
        id: 71,
        title: 'تدفق الماء',
        description: 'وجه تدفق الماء للوصول للهدف',
        type: MotionPuzzleType.fluidDynamics,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 120,
        maxScore: 280,
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        config: {
          'fluidViscosity': 1.0,
          'pipeCount': 5,
          'pressure': 2.0,
        },
        hints: ['اتبع التدفق', 'احسب الضغط', 'استخدم الأنابيب'],
      ),
    ];
  }

  // ألغاز نظام الجسيمات
  static List<MotionPuzzle> _generateParticlePuzzles() {
    return [
      MotionPuzzle(
        id: 81,
        title: 'عاصفة الجسيمات',
        description: 'تحكم في الجسيمات لتكوين الشكل المطلوب',
        type: MotionPuzzleType.particleSystem,
        difficulty: PuzzleDifficulty.expert,
        timeLimit: 150,
        maxScore: 350,
        primaryColor: Colors.pink,
        secondaryColor: Colors.pinkAccent,
        config: {
          'particleCount': 100,
          'targetShape': 'star',
          'attractionForce': 3.0,
        },
        hints: ['اجذب الجسيمات', 'كون الشكل', 'استخدم القوة'],
      ),
    ];
  }

  // ألغاز الذكاء الاصطناعي
  static List<MotionPuzzle> _generateAIPuzzles() {
    return [
      MotionPuzzle(
        id: 91,
        title: 'تحدي الذكاء الاصطناعي',
        description: 'تفاعل مع الذكاء الاصطناعي لحل اللغز',
        type: MotionPuzzleType.aiInteraction,
        difficulty: PuzzleDifficulty.master,
        timeLimit: 180,
        maxScore: 400,
        primaryColor: Colors.deepPurple,
        secondaryColor: Colors.indigo,
        config: {
          'aiDifficulty': 'adaptive',
          'learningRate': 0.1,
          'responseTime': 1.0,
        },
        hints: ['الذكاء الاصطناعي يتعلم', 'غير استراتيجيتك', 'فكر خارج الصندوق'],
      ),
    ];
  }
}

// مدير الألغاز الحركية
class MotionPuzzleManager {
  static List<MotionPuzzle> _allPuzzles = [];
  static int _currentPuzzleIndex = 0;

  static void initialize() {
    _allPuzzles = MotionPuzzleGenerator.generateAllPuzzles();
  }

  static List<MotionPuzzle> getAllPuzzles() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles;
  }

  static MotionPuzzle getCurrentPuzzle() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles[_currentPuzzleIndex % _allPuzzles.length];
  }

  static MotionPuzzle getNextPuzzle() {
    _currentPuzzleIndex++;
    return getCurrentPuzzle();
  }

  static List<MotionPuzzle> getPuzzlesByType(MotionPuzzleType type) {
    return _allPuzzles.where((puzzle) => puzzle.type == type).toList();
  }

  static List<MotionPuzzle> getPuzzlesByDifficulty(PuzzleDifficulty difficulty) {
    return _allPuzzles.where((puzzle) => puzzle.difficulty == difficulty).toList();
  }

  static int getTotalPuzzleCount() {
    return _allPuzzles.length;
  }
}
