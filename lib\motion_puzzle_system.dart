import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:async';
import 'sound_system.dart';

// نظام الألغاز الحركية المتطور
enum MotionPuzzleType {
  dragAndDrop,      // السحب والإفلات
  gesturePattern,   // أنماط الإيماءات
  physicsSimulation, // محاكاة الفيزياء
  colorMatching,    // مطابقة الألوان
  shapeManipulation, // تلاعب الأشكال
  gravityPuzzle,    // ألغاز الجاذبية
  magneticField,    // المجال المغناطيسي
  fluidDynamics,    // ديناميكا السوائل
  particleSystem,   // نظام الجسيمات
  aiInteraction,    // التفاعل مع الذكاء الاصطناعي
}

enum PuzzleDifficulty {
  beginner,    // مبتدئ
  easy,        // سهل
  medium,      // متوسط
  hard,        // صعب
  expert,      // خبير
  master,      // أستاذ
}

// نموذج اللغز الحركي
class MotionPuzzle {
  final int id;
  final String title;
  final String description;
  final MotionPuzzleType type;
  final PuzzleDifficulty difficulty;
  final int timeLimit;
  final int maxScore;
  final Color primaryColor;
  final Color secondaryColor;
  final Map<String, dynamic> config;
  final List<String> hints;

  MotionPuzzle({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.timeLimit,
    required this.maxScore,
    required this.primaryColor,
    required this.secondaryColor,
    required this.config,
    required this.hints,
  });
}

// مولد الألغاز الحركية
class MotionPuzzleGenerator {
  static List<MotionPuzzle> generateAllPuzzles() {
    List<MotionPuzzle> puzzles = [];
    
    // ألغاز السحب والإفلات
    puzzles.addAll(_generateDragDropPuzzles());
    
    // ألغاز الإيماءات
    puzzles.addAll(_generateGesturePuzzles());
    
    // ألغاز الفيزياء
    puzzles.addAll(_generatePhysicsPuzzles());
    
    // ألغاز مطابقة الألوان
    puzzles.addAll(_generateColorPuzzles());
    
    // ألغاز تلاعب الأشكال
    puzzles.addAll(_generateShapePuzzles());
    
    // ألغاز الجاذبية
    puzzles.addAll(_generateGravityPuzzles());
    
    // ألغاز المجال المغناطيسي
    puzzles.addAll(_generateMagneticPuzzles());
    
    // ألغاز ديناميكا السوائل
    puzzles.addAll(_generateFluidPuzzles());
    
    // ألغاز نظام الجسيمات
    puzzles.addAll(_generateParticlePuzzles());
    
    // ألغاز الذكاء الاصطناعي
    puzzles.addAll(_generateAIPuzzles());
    
    return puzzles;
  }

  // ألغاز السحب والإفلات
  static List<MotionPuzzle> _generateDragDropPuzzles() {
    return [
      MotionPuzzle(
        id: 1,
        title: 'ترتيب الألوان الأساسية',
        description: 'اسحب الكرات الملونة إلى الصناديق المطابقة',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 45,
        maxScore: 100,
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        config: {
          'ballCount': 3,
          'colors': ['red', 'blue', 'green'],
          'targetPositions': 3,
        },
        hints: ['ابحث عن اللون المطابق', 'اسحب بحذر', 'الوقت محدود!'],
      ),

      MotionPuzzle(
        id: 2,
        title: 'ترتيب الألوان المتقدم',
        description: 'اسحب 4 كرات ملونة إلى مواضعها الصحيحة',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 60,
        maxScore: 150,
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        config: {
          'ballCount': 4,
          'colors': ['red', 'blue', 'green', 'yellow'],
          'targetPositions': 4,
        },
        hints: ['تذكر الألوان', 'اسحب بدقة', 'لا تتسرع'],
      ),

      MotionPuzzle(
        id: 3,
        title: 'تحدي الألوان الكامل',
        description: 'اسحب 5 كرات ملونة إلى الأهداف المطابقة',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 75,
        maxScore: 200,
        primaryColor: Colors.orange,
        secondaryColor: Colors.deepOrange,
        config: {
          'ballCount': 5,
          'colors': ['red', 'blue', 'green', 'yellow', 'purple'],
          'targetPositions': 5,
        },
        hints: ['خطط قبل السحب', 'تذكر المواضع', 'استخدم الذاكرة'],
      ),

      MotionPuzzle(
        id: 4,
        title: 'خبير الألوان',
        description: 'اسحب 6 كرات ملونة بسرعة ودقة',
        type: MotionPuzzleType.dragAndDrop,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 90,
        maxScore: 250,
        primaryColor: Colors.teal,
        secondaryColor: Colors.cyan,
        config: {
          'ballCount': 6,
          'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
          'targetPositions': 6,
        },
        hints: ['كن سريعاً', 'تذكر جميع الألوان', 'لا تخطئ'],
      ),
    ];
  }

  // ألغاز الإيماءات
  static List<MotionPuzzle> _generateGesturePuzzles() {
    return [
      MotionPuzzle(
        id: 11,
        title: 'رسم الدائرة',
        description: 'ارسم دائرة كاملة بإصبعك',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 30,
        maxScore: 100,
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        config: {
          'pattern': 'circle',
          'accuracy': 0.7,
          'strokeWidth': 5.0,
        },
        hints: ['ارسم ببطء', 'حافظ على الدقة', 'اجعلها دائرة كاملة'],
      ),

      MotionPuzzle(
        id: 12,
        title: 'رسم المربع',
        description: 'ارسم مربعاً بأربعة خطوط مستقيمة',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 40,
        maxScore: 120,
        primaryColor: Colors.indigo,
        secondaryColor: Colors.blue,
        config: {
          'pattern': 'square',
          'accuracy': 0.8,
          'strokeWidth': 5.0,
        },
        hints: ['ارسم خطوط مستقيمة', 'اجعل الزوايا حادة', 'أكمل الشكل'],
      ),

      MotionPuzzle(
        id: 13,
        title: 'رسم المثلث',
        description: 'ارسم مثلثاً بثلاثة خطوط',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 35,
        maxScore: 150,
        primaryColor: Colors.green,
        secondaryColor: Colors.lightGreen,
        config: {
          'pattern': 'triangle',
          'accuracy': 0.75,
          'strokeWidth': 5.0,
        },
        hints: ['ثلاثة خطوط فقط', 'اربط النقاط', 'اجعله متساوي الأضلاع'],
      ),

      MotionPuzzle(
        id: 14,
        title: 'رسم النجمة',
        description: 'ارسم نجمة خماسية',
        type: MotionPuzzleType.gesturePattern,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 50,
        maxScore: 200,
        primaryColor: Colors.amber,
        secondaryColor: Colors.yellow,
        config: {
          'pattern': 'star',
          'accuracy': 0.8,
          'strokeWidth': 5.0,
        },
        hints: ['خمس نقاط', 'ارسم بحركة واحدة', 'تذكر شكل النجمة'],
      ),
    ];
  }

  // ألغاز الفيزياء
  static List<MotionPuzzle> _generatePhysicsPuzzles() {
    return [
      MotionPuzzle(
        id: 21,
        title: 'إطلاق الكرة البسيط',
        description: 'اضغط لإطلاق الكرة نحو الهدف',
        type: MotionPuzzleType.physicsSimulation,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 30,
        maxScore: 100,
        primaryColor: Colors.red,
        secondaryColor: Colors.pink,
        config: {
          'gravity': 5.0,
          'ballCount': 1,
          'targetCount': 1,
          'obstacles': false,
        },
        hints: ['اضغط على الكرة', 'اتجه نحو الهدف الأخضر', 'بسيط جداً!'],
      ),

      MotionPuzzle(
        id: 22,
        title: 'كرة الارتداد',
        description: 'اطلق الكرة لتصل للهدف بعد الارتداد',
        type: MotionPuzzleType.physicsSimulation,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 45,
        maxScore: 150,
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        config: {
          'gravity': 7.0,
          'ballCount': 1,
          'targetCount': 1,
          'obstacles': true,
        },
        hints: ['استخدم الارتداد', 'احسب الزاوية', 'الفيزياء تساعدك'],
      ),

      MotionPuzzle(
        id: 23,
        title: 'تحدي الجاذبية',
        description: 'اطلق الكرة في مجال جاذبية قوي',
        type: MotionPuzzleType.physicsSimulation,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 60,
        maxScore: 200,
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        config: {
          'gravity': 12.0,
          'ballCount': 1,
          'targetCount': 1,
          'obstacles': true,
        },
        hints: ['الجاذبية قوية', 'احسب المسار', 'التوقيت مهم'],
      ),
    ];
  }

  // ألغاز مطابقة الألوان
  static List<MotionPuzzle> _generateColorPuzzles() {
    return [
      MotionPuzzle(
        id: 31,
        title: 'خلط الألوان الأساسية',
        description: 'امزج لونين لإنتاج لون جديد',
        type: MotionPuzzleType.colorMatching,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 45,
        maxScore: 120,
        primaryColor: Colors.yellow,
        secondaryColor: Colors.amber,
        config: {
          'targetColor': 'any',
          'availableColors': ['red', 'blue', 'yellow'],
          'mixingRatio': [0.5, 0.5],
        },
        hints: ['اختر لونين مختلفين', 'شاهد النتيجة', 'جرب تركيبات مختلفة'],
      ),

      MotionPuzzle(
        id: 32,
        title: 'صانع الألوان',
        description: 'اكتشف جميع الألوان الممكنة',
        type: MotionPuzzleType.colorMatching,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 60,
        maxScore: 150,
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        config: {
          'targetColor': 'multiple',
          'availableColors': ['red', 'blue', 'yellow'],
          'mixingRatio': [0.5, 0.5],
        },
        hints: ['جرب كل التركيبات', 'أحمر + أزرق = بنفسجي', 'أصفر + أحمر = برتقالي'],
      ),
    ];
  }

  // ألغاز تلاعب الأشكال
  static List<MotionPuzzle> _generateShapePuzzles() {
    return [
      MotionPuzzle(
        id: 41,
        title: 'تحويل الشكل البسيط',
        description: 'اضغط 5 مرات لتحويل المربع إلى دائرة',
        type: MotionPuzzleType.shapeManipulation,
        difficulty: PuzzleDifficulty.beginner,
        timeLimit: 30,
        maxScore: 100,
        primaryColor: Colors.orange,
        secondaryColor: Colors.deepOrange,
        config: {
          'initialShape': 'square',
          'targetShape': 'circle',
          'transformationSteps': 5,
        },
        hints: ['اضغط على الشكل', 'شاهد التحول', 'استمر حتى النهاية'],
      ),

      MotionPuzzle(
        id: 42,
        title: 'تحويل الشكل المتقدم',
        description: 'اضغط 8 مرات لتحويل الشكل بالكامل',
        type: MotionPuzzleType.shapeManipulation,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 45,
        maxScore: 150,
        primaryColor: Colors.teal,
        secondaryColor: Colors.cyan,
        config: {
          'initialShape': 'square',
          'targetShape': 'circle',
          'transformationSteps': 8,
        },
        hints: ['المزيد من الضغطات', 'تحول تدريجي', 'صبر قليل'],
      ),

      MotionPuzzle(
        id: 43,
        title: 'خبير التحويل',
        description: 'اضغط 10 مرات لإتقان تحويل الأشكال',
        type: MotionPuzzleType.shapeManipulation,
        difficulty: PuzzleDifficulty.medium,
        timeLimit: 60,
        maxScore: 200,
        primaryColor: Colors.indigo,
        secondaryColor: Colors.deepPurple,
        config: {
          'initialShape': 'square',
          'targetShape': 'circle',
          'transformationSteps': 10,
        },
        hints: ['تحدي حقيقي', 'ضغطات كثيرة', 'النتيجة تستحق'],
      ),
    ];
  }

  // ألغاز الجاذبية - مبسطة
  static List<MotionPuzzle> _generateGravityPuzzles() {
    return [
      MotionPuzzle(
        id: 51,
        title: 'لغز الجاذبية البسيط',
        description: 'اضغط لتفعيل تأثير الجاذبية',
        type: MotionPuzzleType.gravityPuzzle,
        difficulty: PuzzleDifficulty.easy,
        timeLimit: 40,
        maxScore: 120,
        primaryColor: Colors.deepPurple,
        secondaryColor: Colors.purple,
        config: {
          'gravityDirection': 'down',
          'canFlip': false,
          'ballCount': 1,
        },
        hints: ['اضغط لبدء الجاذبية', 'شاهد الكرة تسقط', 'بسيط جداً'],
      ),
    ];
  }

  // ألغاز المجال المغناطيسي
  static List<MotionPuzzle> _generateMagneticPuzzles() {
    return [
      MotionPuzzle(
        id: 61,
        title: 'المغناطيس القوي',
        description: 'استخدم المغناطيس لجذب الكرات المعدنية',
        type: MotionPuzzleType.magneticField,
        difficulty: PuzzleDifficulty.expert,
        timeLimit: 90,
        maxScore: 300,
        primaryColor: Colors.blueGrey,
        secondaryColor: Colors.grey,
        config: {
          'magnetStrength': 5.0,
          'metalBalls': 6,
          'magnetCount': 2,
        },
        hints: ['الأضداد تتجاذب', 'قوة المجال', 'احسب المسافة'],
      ),
    ];
  }

  // ألغاز ديناميكا السوائل
  static List<MotionPuzzle> _generateFluidPuzzles() {
    return [
      MotionPuzzle(
        id: 71,
        title: 'تدفق الماء',
        description: 'وجه تدفق الماء للوصول للهدف',
        type: MotionPuzzleType.fluidDynamics,
        difficulty: PuzzleDifficulty.hard,
        timeLimit: 120,
        maxScore: 280,
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        config: {
          'fluidViscosity': 1.0,
          'pipeCount': 5,
          'pressure': 2.0,
        },
        hints: ['اتبع التدفق', 'احسب الضغط', 'استخدم الأنابيب'],
      ),
    ];
  }

  // ألغاز نظام الجسيمات
  static List<MotionPuzzle> _generateParticlePuzzles() {
    return [
      MotionPuzzle(
        id: 81,
        title: 'عاصفة الجسيمات',
        description: 'تحكم في الجسيمات لتكوين الشكل المطلوب',
        type: MotionPuzzleType.particleSystem,
        difficulty: PuzzleDifficulty.expert,
        timeLimit: 150,
        maxScore: 350,
        primaryColor: Colors.pink,
        secondaryColor: Colors.pinkAccent,
        config: {
          'particleCount': 100,
          'targetShape': 'star',
          'attractionForce': 3.0,
        },
        hints: ['اجذب الجسيمات', 'كون الشكل', 'استخدم القوة'],
      ),
    ];
  }

  // ألغاز الذكاء الاصطناعي
  static List<MotionPuzzle> _generateAIPuzzles() {
    return [
      MotionPuzzle(
        id: 91,
        title: 'تحدي الذكاء الاصطناعي',
        description: 'تفاعل مع الذكاء الاصطناعي لحل اللغز',
        type: MotionPuzzleType.aiInteraction,
        difficulty: PuzzleDifficulty.master,
        timeLimit: 180,
        maxScore: 400,
        primaryColor: Colors.deepPurple,
        secondaryColor: Colors.indigo,
        config: {
          'aiDifficulty': 'adaptive',
          'learningRate': 0.1,
          'responseTime': 1.0,
        },
        hints: ['الذكاء الاصطناعي يتعلم', 'غير استراتيجيتك', 'فكر خارج الصندوق'],
      ),
    ];
  }
}

// مدير الألغاز الحركية المحسن
class MotionPuzzleManager {
  static List<MotionPuzzle> _allPuzzles = [];
  static final List<int> _usedPuzzleIds = [];
  static int _currentPuzzleIndex = 0;

  static void initialize() {
    if (_allPuzzles.isEmpty) {
      _allPuzzles = MotionPuzzleGenerator.generateAllPuzzles();
      _usedPuzzleIds.clear();
      _currentPuzzleIndex = 0;
      debugPrint('تم تحميل ${_allPuzzles.length} لغز حركي');
    }
  }

  static List<MotionPuzzle> getAllPuzzles() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles;
  }

  static MotionPuzzle getCurrentPuzzle() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }

    // إذا تم استخدام جميع الألغاز، أعد تشغيل القائمة
    if (_usedPuzzleIds.length >= _allPuzzles.length) {
      _usedPuzzleIds.clear();
      _currentPuzzleIndex = 0;
      debugPrint('تم إعادة تشغيل قائمة الألغاز');
    }

    return _allPuzzles[_currentPuzzleIndex % _allPuzzles.length];
  }

  static MotionPuzzle getNextPuzzle() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }

    // البحث عن لغز لم يتم استخدامه
    int attempts = 0;
    while (attempts < _allPuzzles.length) {
      _currentPuzzleIndex = (_currentPuzzleIndex + 1) % _allPuzzles.length;
      MotionPuzzle nextPuzzle = _allPuzzles[_currentPuzzleIndex];

      if (!_usedPuzzleIds.contains(nextPuzzle.id)) {
        _usedPuzzleIds.add(nextPuzzle.id);
        debugPrint('اللغز التالي: ${nextPuzzle.title} (ID: ${nextPuzzle.id})');
        return nextPuzzle;
      }
      attempts++;
    }

    // إذا تم استخدام جميع الألغاز، أعد تشغيل القائمة
    _usedPuzzleIds.clear();
    _currentPuzzleIndex = 0;
    MotionPuzzle firstPuzzle = _allPuzzles[0];
    _usedPuzzleIds.add(firstPuzzle.id);
    debugPrint('إعادة تشغيل: ${firstPuzzle.title}');
    return firstPuzzle;
  }

  static List<MotionPuzzle> getPuzzlesByType(MotionPuzzleType type) {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles.where((puzzle) => puzzle.type == type).toList();
  }

  static List<MotionPuzzle> getPuzzlesByDifficulty(PuzzleDifficulty difficulty) {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles.where((puzzle) => puzzle.difficulty == difficulty).toList();
  }

  static int getTotalPuzzleCount() {
    if (_allPuzzles.isEmpty) {
      initialize();
    }
    return _allPuzzles.length;
  }

  static void resetProgress() {
    _usedPuzzleIds.clear();
    _currentPuzzleIndex = 0;
    debugPrint('تم إعادة تعيين تقدم الألغاز');
  }

  static Map<String, dynamic> getProgress() {
    return {
      'totalPuzzles': _allPuzzles.length,
      'usedPuzzles': _usedPuzzleIds.length,
      'currentIndex': _currentPuzzleIndex,
      'completionPercentage': _allPuzzles.isEmpty ? 0 : (_usedPuzzleIds.length / _allPuzzles.length * 100).round(),
    };
  }
}
