import 'package:flutter/material.dart';
import 'game_data.dart';
import 'advanced_game_system.dart';

// نموذج المرحلة
class GameLevel {
  final int id;
  final String name;
  final String description;
  final String theme; // الثيم البصري
  final Color primaryColor;
  final Color secondaryColor;
  final IconData icon;
  final DifficultyLevel difficulty;
  final List<QuestionCategory> categories;
  final int requiredScore; // النقاط المطلوبة لفتح المرحلة
  final int questionsCount; // عدد الأسئلة في المرحلة
  final int timeLimit; // الوقت المحدد لكل سؤال
  final List<String> rewards; // المكافآت عند إكمال المرحلة
  final bool isUnlocked;
  final int starsEarned; // النجوم المحققة (0-3)
  final int bestScore; // أفضل نقاط محققة

  GameLevel({
    required this.id,
    required this.name,
    required this.description,
    required this.theme,
    required this.primaryColor,
    required this.secondaryColor,
    required this.icon,
    required this.difficulty,
    required this.categories,
    required this.requiredScore,
    required this.questionsCount,
    required this.timeLimit,
    required this.rewards,
    this.isUnlocked = false,
    this.starsEarned = 0,
    this.bestScore = 0,
  });

  // حساب النجوم بناءً على النقاط
  int calculateStars(int score) {
    double percentage = score / (questionsCount * 20); // افتراض 20 نقطة لكل سؤال
    if (percentage >= 0.9) return 3; // 90% فأكثر = 3 نجوم
    if (percentage >= 0.7) return 2; // 70% فأكثر = نجمتان
    if (percentage >= 0.5) return 1; // 50% فأكثر = نجمة واحدة
    return 0; // أقل من 50% = لا نجوم
  }

  GameLevel copyWith({
    bool? isUnlocked,
    int? starsEarned,
    int? bestScore,
  }) {
    return GameLevel(
      id: id,
      name: name,
      description: description,
      theme: theme,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      icon: icon,
      difficulty: difficulty,
      categories: categories,
      requiredScore: requiredScore,
      questionsCount: questionsCount,
      timeLimit: timeLimit,
      rewards: rewards,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      starsEarned: starsEarned ?? this.starsEarned,
      bestScore: bestScore ?? this.bestScore,
    );
  }
}

// مدير نظام المراحل
class LevelManager {
  static List<GameLevel> _levels = [];
  static int _currentLevelId = 1;

  // تهيئة المراحل
  static void initializeLevels() {
    _levels = [
      // المرحلة 1: البداية
      GameLevel(
        id: 1,
        name: "البداية",
        description: "ابدأ رحلتك مع ألغاز بسيطة",
        theme: "beginner",
        primaryColor: Colors.green,
        secondaryColor: Colors.lightGreen,
        icon: Icons.play_arrow,
        difficulty: DifficultyLevel.beginner,
        categories: [QuestionCategory.general, QuestionCategory.math],
        requiredScore: 0,
        questionsCount: 10,
        timeLimit: 45,
        rewards: ["10 نقاط إضافية", "فتح المرحلة التالية"],
        isUnlocked: true,
      ),

      // المرحلة 2: الألوان والأشكال
      GameLevel(
        id: 2,
        name: "الألوان والأشكال",
        description: "اختبر معرفتك بالألوان والأشكال",
        theme: "colors",
        primaryColor: Colors.blue,
        secondaryColor: Colors.lightBlue,
        icon: Icons.palette,
        difficulty: DifficultyLevel.easy,
        categories: [QuestionCategory.art, QuestionCategory.general],
        requiredScore: 80,
        questionsCount: 15,
        timeLimit: 40,
        rewards: ["20 نقاط إضافية", "شارة الألوان"],
      ),

      // المرحلة 3: عالم الحيوانات
      GameLevel(
        id: 3,
        name: "عالم الحيوانات",
        description: "اكتشف أسرار المملكة الحيوانية",
        theme: "animals",
        primaryColor: Colors.orange,
        secondaryColor: Colors.deepOrange,
        icon: Icons.pets,
        difficulty: DifficultyLevel.easy,
        categories: [QuestionCategory.animals, QuestionCategory.nature],
        requiredScore: 150,
        questionsCount: 15,
        timeLimit: 35,
        rewards: ["25 نقاط إضافية", "شارة الحيوانات"],
      ),

      // المرحلة 4: الرياضيات الممتعة
      GameLevel(
        id: 4,
        name: "الرياضيات الممتعة",
        description: "تحدى عقلك مع الأرقام",
        theme: "math",
        primaryColor: Colors.purple,
        secondaryColor: Colors.deepPurple,
        icon: Icons.calculate,
        difficulty: DifficultyLevel.medium,
        categories: [QuestionCategory.math],
        requiredScore: 250,
        questionsCount: 20,
        timeLimit: 30,
        rewards: ["30 نقاط إضافية", "شارة الرياضيات"],
      ),

      // المرحلة 5: العلوم والاكتشافات
      GameLevel(
        id: 5,
        name: "العلوم والاكتشافات",
        description: "استكشف عجائب العلم",
        theme: "science",
        primaryColor: Colors.teal,
        secondaryColor: Colors.cyan,
        icon: Icons.science,
        difficulty: DifficultyLevel.medium,
        categories: [QuestionCategory.science, QuestionCategory.space],
        requiredScore: 400,
        questionsCount: 20,
        timeLimit: 30,
        rewards: ["35 نقاط إضافية", "شارة العلوم"],
      ),

      // المرحلة 6: حول العالم
      GameLevel(
        id: 6,
        name: "حول العالم",
        description: "سافر عبر القارات والثقافات",
        theme: "geography",
        primaryColor: Colors.indigo,
        secondaryColor: Colors.blue,
        icon: Icons.public,
        difficulty: DifficultyLevel.medium,
        categories: [QuestionCategory.geography, QuestionCategory.history],
        requiredScore: 600,
        questionsCount: 25,
        timeLimit: 25,
        rewards: ["40 نقاط إضافية", "شارة المسافر"],
      ),

      // المرحلة 7: الرياضة والمرح
      GameLevel(
        id: 7,
        name: "الرياضة والمرح",
        description: "اختبر معلوماتك الرياضية",
        theme: "sports",
        primaryColor: Colors.red,
        secondaryColor: Colors.pink,
        icon: Icons.sports_soccer,
        difficulty: DifficultyLevel.hard,
        categories: [QuestionCategory.sports],
        requiredScore: 800,
        questionsCount: 25,
        timeLimit: 25,
        rewards: ["45 نقاط إضافية", "شارة البطل"],
      ),

      // المرحلة 8: التكنولوجيا والمستقبل
      GameLevel(
        id: 8,
        name: "التكنولوجيا والمستقبل",
        description: "اكتشف عالم التقنية",
        theme: "technology",
        primaryColor: Colors.grey,
        secondaryColor: Colors.blueGrey,
        icon: Icons.computer,
        difficulty: DifficultyLevel.hard,
        categories: [QuestionCategory.technology],
        requiredScore: 1000,
        questionsCount: 30,
        timeLimit: 20,
        rewards: ["50 نقاط إضافية", "شارة التقنية"],
      ),

      // المرحلة 9: الألغاز المخادعة
      GameLevel(
        id: 9,
        name: "الألغاز المخادعة",
        description: "فكر خارج الصندوق",
        theme: "tricky",
        primaryColor: Colors.deepOrange,
        secondaryColor: Colors.orange,
        icon: Icons.psychology,
        difficulty: DifficultyLevel.expert,
        categories: [QuestionCategory.general],
        requiredScore: 1500,
        questionsCount: 30,
        timeLimit: 20,
        rewards: ["60 نقاط إضافية", "شارة العبقري"],
      ),

      // المرحلة 10: تحدي الأساطير
      GameLevel(
        id: 10,
        name: "تحدي الأساطير",
        description: "المرحلة الأصعب للأبطال فقط",
        theme: "legendary",
        primaryColor: Colors.amber,
        secondaryColor: Colors.yellow,
        icon: Icons.emoji_events,
        difficulty: DifficultyLevel.genius,
        categories: [
          QuestionCategory.math,
          QuestionCategory.science,
          QuestionCategory.geography,
          QuestionCategory.history,
        ],
        requiredScore: 2000,
        questionsCount: 50,
        timeLimit: 15,
        rewards: ["100 نقاط إضافية", "لقب الأسطورة", "شارة ذهبية"],
      ),
    ];

    // تحديث حالة المراحل بناءً على النقاط الحالية
    _updateLevelsStatus();
  }

  // تحديث حالة المراحل
  static void _updateLevelsStatus() {
    int currentScore = AdvancedGameSystem.totalScore;
    
    for (int i = 0; i < _levels.length; i++) {
      if (currentScore >= _levels[i].requiredScore) {
        _levels[i] = _levels[i].copyWith(isUnlocked: true);
      }
    }
  }

  // الحصول على جميع المراحل
  static List<GameLevel> getAllLevels() {
    if (_levels.isEmpty) {
      initializeLevels();
    }
    _updateLevelsStatus();
    return _levels;
  }

  // الحصول على مرحلة محددة
  static GameLevel? getLevel(int id) {
    try {
      return _levels.firstWhere((level) => level.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على المرحلة الحالية
  static GameLevel getCurrentLevel() {
    return getLevel(_currentLevelId) ?? _levels.first;
  }

  // تعيين المرحلة الحالية
  static void setCurrentLevel(int levelId) {
    _currentLevelId = levelId;
  }

  // إكمال مرحلة وتحديث النجوم
  static void completeLevel(int levelId, int score) {
    int levelIndex = _levels.indexWhere((level) => level.id == levelId);
    if (levelIndex != -1) {
      GameLevel level = _levels[levelIndex];
      int stars = level.calculateStars(score);
      int newBestScore = score > level.bestScore ? score : level.bestScore;
      int newStars = stars > level.starsEarned ? stars : level.starsEarned;
      
      _levels[levelIndex] = level.copyWith(
        starsEarned: newStars,
        bestScore: newBestScore,
      );
      
      // فتح المرحلة التالية إذا كانت موجودة
      if (levelIndex + 1 < _levels.length) {
        _levels[levelIndex + 1] = _levels[levelIndex + 1].copyWith(isUnlocked: true);
      }
    }
  }

  // الحصول على إجمالي النجوم
  static int getTotalStars() {
    return _levels.fold(0, (sum, level) => sum + level.starsEarned);
  }

  // الحصول على عدد المراحل المكتملة
  static int getCompletedLevelsCount() {
    return _levels.where((level) => level.starsEarned > 0).length;
  }

  // الحصول على أسئلة مرحلة محددة
  static List<Question> getLevelQuestions(int levelId) {
    GameLevel? level = getLevel(levelId);
    if (level == null) return [];

    List<Question> allQuestions = MegaPuzzleGenerator.generateAllPuzzles();
    
    // تصفية الأسئلة حسب فئات المرحلة وصعوبتها
    List<Question> levelQuestions = allQuestions.where((question) {
      return level.categories.contains(question.category) &&
             question.difficulty == level.difficulty;
    }).toList();

    // خلط الأسئلة وأخذ العدد المطلوب
    levelQuestions.shuffle();
    return levelQuestions.take(level.questionsCount).toList();
  }
}
